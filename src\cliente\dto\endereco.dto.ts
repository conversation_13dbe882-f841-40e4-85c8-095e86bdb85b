import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';
import { IsValidUF } from '../../common/validators/uf.validator';

export class EnderecoDto {
  @ApiProperty({ example: '<PERSON><PERSON> das <PERSON>', description: 'Nome da rua' })
  @IsNotEmpty()
  @IsString()
  rua: string;

  @ApiProperty({ example: '123', description: 'Número do endereço' })
  @IsNotEmpty()
  @IsString()
  numero: string;

  @ApiProperty({ example: 'Centro', description: 'Bairro' })
  @IsNotEmpty()
  @IsString()
  bairro: string;

  @ApiProperty({ example: 'São Paulo', description: 'Cidade' })
  @IsNotEmpty()
  @IsString()
  cidade: string;

  @ApiProperty({
    example: 'SP',
    description: 'Estado (UF) - deve ser uma UF válida do Brasil',
    enum: ['AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO']
  })
  @IsNotEmpty()
  @IsString()
  @IsValidUF()
  estado: string;

  @ApiProperty({ example: '01234-567', description: 'CEP' })
  @IsNotEmpty()
  @IsString()
  cep: string;
}
