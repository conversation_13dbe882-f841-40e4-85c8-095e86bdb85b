import { <PERSON>du<PERSON> } from '@nestjs/common';
import { ProtocoloController } from './protocolo.controller';
import { ProtocoloService } from './protocolo.service';
import { ProtocoloIntegrationService } from './services/protocolo-integration.service';
import { IntegrationConfigService } from '../common/services/integration-config.service';

@Module({
  controllers: [ProtocoloController],
  providers: [
    ProtocoloService,
    ProtocoloIntegrationService,
    IntegrationConfigService,
  ],
})
export class ProtocoloModule {}