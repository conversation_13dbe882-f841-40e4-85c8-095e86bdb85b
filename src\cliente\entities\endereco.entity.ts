import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, OneToOne, JoinColumn } from 'typeorm';
import { <PERSON><PERSON><PERSON> } from './cliente.entity';

@Entity()
export class Endereco {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  rua: string;

  @Column()
  numero: string;

  @Column()
  bairro: string;

  @Column()
  cidade: string;

  @Column()
  estado: string;

  @Column()
  cep: string;
}