import { Modu<PERSON> } from '@nestjs/common';
import { PostoController } from './posto.controller';
import { PostoService } from './posto.service';
import { PostoIntegrationService } from './services/posto-integration.service';
import { IntegrationConfigService } from '../common/services/integration-config.service';

@Module({
  controllers: [PostoController],
  providers: [PostoService, PostoIntegrationService, IntegrationConfigService],
  exports: [PostoService],
})
export class PostoModule {}
