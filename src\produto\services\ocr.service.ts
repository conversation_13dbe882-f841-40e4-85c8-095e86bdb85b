import { Injectable, Logger } from '@nestjs/common';
import { AIDocumentAnalyzerService } from './ai-document-analyzer.service';

export interface NotaFiscalData {
  dataEmissao: Date | null;
  itens: string[];
  cep: string | null;
  textoCompleto: string;
  tipoDocumento: 'image' | 'pdf';
  confianca: number; // 0-100, confiança da IA na extração
}

@Injectable()
export class OcrService {
  private readonly logger = new Logger(OcrService.name);

  constructor(private readonly aiAnalyzer: AIDocumentAnalyzerService) {}

  async processNotaFiscal(base64Document: string): Promise<NotaFiscalData> {
    try {
      this.logger.log('Iniciando processamento IA da nota fiscal');

      // Detecta o tipo de documento
      const tipoDocumento = this.detectDocumentType(base64Document);

      // Usa IA para extrair informações
      const aiResult = await this.aiAnalyzer.analyzeDocument(base64Document, tipoDocumento);

      this.logger.log(`Processamento IA concluído com sucesso (${aiResult.provider}, confiança: ${aiResult.confianca}%)`);

      return {
        dataEmissao: aiResult.dataEmissao,
        itens: aiResult.itens,
        cep: aiResult.cep,
        confianca: aiResult.confianca,
        tipoDocumento,
        textoCompleto: aiResult.rawResponse
      };
    } catch (error) {
      this.logger.error(`Erro ao processar documento: ${error.message}`, error.stack);
      throw new Error('Erro ao processar nota fiscal');
    }
  }

  private detectDocumentType(base64Document: string): 'image' | 'pdf' {
    // Remove prefixo se existir
    const cleanBase64 = base64Document.replace(/^data:[^;]+;base64,/, '');

    // Converte para buffer para analisar os primeiros bytes
    const buffer = Buffer.from(cleanBase64, 'base64');

    // Verifica assinatura do PDF (%PDF)
    if (buffer.toString('ascii', 0, 4) === '%PDF') {
      return 'pdf';
    }

    // Verifica assinaturas de imagem
    const imageSignatures = [
      [0xFF, 0xD8, 0xFF], // JPEG
      [0x89, 0x50, 0x4E, 0x47], // PNG
      [0x47, 0x49, 0x46], // GIF
      [0x42, 0x4D], // BMP
    ];

    for (const signature of imageSignatures) {
      if (signature.every((byte, index) => buffer[index] === byte)) {
        return 'image';
      }
    }

    // Default para imagem se não conseguir detectar
    return 'image';
  }





}
