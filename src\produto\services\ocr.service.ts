import { Injectable, Logger } from '@nestjs/common';
import { createWorker } from 'tesseract.js';

export interface NotaFiscalData {
  dataEmissao: Date | null;
  itens: string[];
  cep: string | null;
  textoCompleto: string;
}

@Injectable()
export class OcrService {
  private readonly logger = new Logger(OcrService.name);

  async processNotaFiscal(base64Image: string): Promise<NotaFiscalData> {
    try {
      this.logger.log('Iniciando processamento OCR da nota fiscal');

      // Remove o prefixo data:image/... se existir
      const cleanBase64 = base64Image.replace(/^data:image\/[a-z]+;base64,/, '');
      
      // Converte base64 para buffer
      const imageBuffer = Buffer.from(cleanBase64, 'base64');

      // Cria worker do Tesseract
      const worker = await createWorker('por');

      // Processa a imagem
      const { data: { text } } = await worker.recognize(imageBuffer);
      
      await worker.terminate();

      this.logger.log('OCR processado com sucesso');

      // Extrai informações da nota fiscal
      const notaFiscalData = this.extractNotaFiscalInfo(text);

      return notaFiscalData;
    } catch (error) {
      this.logger.error(`Erro ao processar OCR: ${error.message}`, error.stack);
      throw new Error('Erro ao processar nota fiscal');
    }
  }

  private extractNotaFiscalInfo(text: string): NotaFiscalData {
    this.logger.log('Extraindo informações da nota fiscal');

    const result: NotaFiscalData = {
      dataEmissao: null,
      itens: [],
      cep: null,
      textoCompleto: text
    };

    // Extrai data de emissão
    result.dataEmissao = this.extractDataEmissao(text);

    // Extrai itens/produtos
    result.itens = this.extractItens(text);

    // Extrai CEP
    result.cep = this.extractCep(text);

    return result;
  }

private extractDataEmissao(text: string): Date | null {
  // Padrões comuns para "Data de autorização" em notas fiscais
  const datePatterns = [
    /data\s*de\s*autoriza[çc][ãa]o[:\s]*(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/i,
    /autoriza[çc][ãa]o[:\s]*(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/i,
    /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/g
  ];

  for (const pattern of datePatterns) {
    const match = text.match(pattern);
    if (match) {
      try {
        const day = parseInt(match[1]);
        const month = parseInt(match[2]);
        const year = parseInt(match[3]);

        if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 2000) {
          return new Date(year, month - 1, day);
        }
      } catch (error) {
        continue;
      }
    }
  }

  return null;
}

  private extractItens(text: string): string[] {
    const itens: string[] = [];
    
    // Padrões para identificar produtos/itens
    const itemPatterns = [
      /(?:produto|item|descri[çc][ãa]o)[:\s]*([^\n\r]+)/gi,
      /\d+\s+([A-Z][A-Z\s]+[A-Z])\s+\d+/g,
      /^[A-Z][A-Z\s]{10,}$/gm
    ];

    for (const pattern of itemPatterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const item = match[1]?.trim();
        if (item && item.length > 3 && !itens.includes(item)) {
          itens.push(item);
        }
      }
    }

    // Se não encontrou itens com padrões específicos, tenta extrair linhas que parecem produtos
    if (itens.length === 0) {
      const lines = text.split('\n');
      for (const line of lines) {
        const cleanLine = line.trim();
        if (cleanLine.length > 10 && 
            /[A-Z]/.test(cleanLine) && 
            !cleanLine.includes('CNPJ') && 
            !cleanLine.includes('CPF') &&
            !cleanLine.includes('TOTAL') &&
            !cleanLine.includes('VALOR')) {
          itens.push(cleanLine);
        }
      }
    }

    return itens.slice(0, 10); // Limita a 10 itens para evitar ruído
  }

  private extractCep(text: string): string | null {
    // Padrão para CEP brasileiro
    const cepPattern = /(\d{5})-?(\d{3})/g;
    
    let match;
    while ((match = cepPattern.exec(text)) !== null) {
      const cep = match[1] + match[2];
      // Valida se é um CEP válido (não pode ser 00000000)
      if (cep !== '00000000') {
        return cep;
      }
    }

    return null;
  }
}
