import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO para enviar dados para a API externa (POST)
 */
export class ExternalProdutoRequestDto {
  @ApiProperty({ example: 'TESTE', description: 'Referência do produto' })
  referencia: string;

  @ApiProperty({ example: 'teste de descrição', description: 'Descrição do produto' })
  descricao: string;

  @ApiProperty({ example: '01', description: 'Código da família' })
  codigoFamilia: string;

  @ApiProperty({ example: '01', description: 'Código da linha' })
  codigoLinha: string;

  @ApiProperty({ example: '12', description: 'Garantia em meses' })
  garantia: string;

  @ApiProperty({ example: '0', description: 'Mão de obra' })
  maoDeObra: string;

  @ApiProperty({ example: '0', description: 'Mão de obra admin' })
  maoDeObraAdmin: string;

  @ApiProperty({ example: true, description: 'Número de série obrigatório' })
  numeroSerieObrigatorio: boolean;

  @ApiProperty({ example: true, description: 'Produto ativo' })
  ativo: boolean;
}

/**
 * DTO para receber dados da API externa (GET)
 */
export class ExternalProdutoResponseDto {
  @ApiProperty({ example: 408790, description: 'ID do produto' })
  produto: number;

  @ApiProperty({ example: 'AUTOCLAVE AMORA 04 INOX 127-220V LILAS PORTUGUES N', description: 'Descrição do produto' })
  descricao: string;

  @ApiProperty({ example: 'AM104INLIPTN', description: 'Referência do produto' })
  referencia: string;

  @ApiProperty({ example: 24, description: 'Garantia em meses' })
  garantia: number;

  @ApiProperty({ example: 1, description: 'Status ativo (1 = ativo, 0 = inativo)' })
  ativo: number;

  @ApiProperty({ example: null, description: 'ID externo' })
  externalId: string | null;

  @ApiProperty({ description: 'Informações da família' })
  familia: {
    familia: number;
    descricao: string;
    codigoFamilia?: string;
    ativo: boolean;
  };

  @ApiProperty({ description: 'Informações da linha' })
  linha: {
    linha: number;
    nome: string;
    codigoLinha: string;
    ativo: boolean;
  };

  @ApiProperty({ example: 'BIVOLT', description: 'Voltagem do produto', required: false })
  voltagem?: string | null;

  @ApiProperty({ example: 'NAC', description: 'Origem do produto' })
  origem: string;

  @ApiProperty({ example: '0', description: 'Mão de obra', required: false })
  maoDeObra?: string;

  @ApiProperty({ example: '0', description: 'Mão de obra admin', required: false })
  maoDeObraAdmin?: string;
}
