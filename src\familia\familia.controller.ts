import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { FamiliaService } from './familia.service';
import { FamiliaDto } from './dto/familia.dto';

@ApiTags('familias')
@Controller('familias')
export class FamiliaController {
  constructor(private readonly familiaService: FamiliaService) {}

  @Get()
  @ApiOperation({ summary: 'Listar todas as famílias' })
  @ApiResponse({ status: 200, description: 'Lista de famílias retornada com sucesso', type: [FamiliaDto] })
  findAll(): Promise<FamiliaDto[]> {
    return this.familiaService.findAll();
  }

  @Get('codigo/:codigo')
  @ApiOperation({ summary: 'Buscar uma família pelo código' })
  @ApiParam({ name: 'codigo', description: '<PERSON><PERSON><PERSON> da família' })
  @ApiResponse({ status: 200, description: 'Família encontrada', type: FamiliaDto })
  @ApiResponse({ status: 404, description: 'Família não encontrada' })
  findByCodigo(@Param('codigo') codigo: string): Promise<FamiliaDto> {
    return this.familiaService.findByCodigo(codigo);
  }
}
