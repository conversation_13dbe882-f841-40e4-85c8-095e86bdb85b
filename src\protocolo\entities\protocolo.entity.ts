import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Cliente } from '../../cliente/entities/cliente.entity';
import { Produto } from '../../produto/entities/produto.entity';
import { Status } from './status.entity';

@Entity()
export class Protocolo {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Cliente, cliente => cliente.protocolos)
  cliente: Cliente;

  @ManyToOne(() => Produto, produto => produto.protocolos)
  produto: Produto;

  @Column()
  nf: string;

  @Column()
  dataCompra: string;

  @Column()
  descricao: string;

  @Column()
  status: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => Status, status => status.protocolo)
  historicoStatus: Status[];
}