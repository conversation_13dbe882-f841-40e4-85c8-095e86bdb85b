import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

export function IsValidUF(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidUF',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value) return true; // Se for opcional, permite vazio
          
          if (typeof value !== 'string') return false;
          
          const uf = value.toUpperCase().trim();
          
          // Lista de UFs válidas do Brasil
          const validUFs = [
            'AC', // Acre
            'AL', // Alagoas
            'AP', // Amapá
            'AM', // Amazonas
            'BA', // Bahia
            'CE', // Ceará
            'DF', // Distrito Federal
            'ES', // Espírito Santo
            'GO', // Goiás
            'MA', // Maranhão
            'MT', // <PERSON><PERSON>
            'MS', // Mato Grosso do Sul
            'MG', // Minas Gerais
            'PA', // Par<PERSON>
            'PB', // Paraíba
            'PR', // Paraná
            'PE', // Pernambuco
            'PI', // Piauí
            'RJ', // Rio de Janeiro
            'RN', // Rio Grande do Norte
            'RS', // Rio Grande do Sul
            'RO', // Rondônia
            'RR', // Roraima
            'SC', // Santa Catarina
            'SP', // São Paulo
            'SE', // Sergipe
            'TO'  // Tocantins
          ];
          
          return validUFs.includes(uf);
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} deve ser uma UF válida do Brasil (ex: SP, RJ, MG, etc.)`;
        },
      },
    });
  };
}

// Função utilitária para obter lista de UFs válidas
export function getValidUFs(): string[] {
  return [
    'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA',
    'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN',
    'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
  ];
}

// Função utilitária para validar UF
export function isValidUF(uf: string): boolean {
  if (!uf || typeof uf !== 'string') return false;
  return getValidUFs().includes(uf.toUpperCase().trim());
}
