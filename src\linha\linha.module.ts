import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { LinhaController } from './linha.controller';
import { LinhaService } from './linha.service';
import { LinhaIntegrationService } from './services/linha-integration.service';
import { IntegrationConfigService } from '../common/services/integration-config.service';

@Module({
  controllers: [LinhaController],
  providers: [LinhaService, LinhaIntegrationService, IntegrationConfigService],
  exports: [LinhaService],
})
export class LinhaModule {}
