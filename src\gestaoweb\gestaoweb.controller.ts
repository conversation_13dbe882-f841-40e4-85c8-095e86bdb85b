import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { GestaowebService } from './gestaoweb.service';
import { CreateGestaowebDto } from './dto/create-gestaoweb.dto';
import { UpdateGestaowebDto } from './dto/update-gestaoweb.dto';
import { ApiQuery } from '@nestjs/swagger';

@Controller('gestaoweb')
export class GestaowebController {
  constructor(private readonly gestaowebService: GestaowebService) {}


  @Get('/obter_coleta')
  @ApiQuery({ name: 'protocoloOrdemServico', required: true, description: 'Filtrar por numero do protocolo ou ordem de serviço' })
  @ApiQuery({ name: 'status', required: true, description: 'Filtrar por status' })
  getCollect(@Query('protocoloOrdemServico') protocoloOrdemServico: string,
      @Query('status') status: string) {
    return this.gestaowebService.getCollect(protocoloOrdemServico, status);
  }

  @Get('/obter_entrega')
  @ApiQuery({ name: 'protocoloOrdemServico', required: true, description: 'Filtrar por numero do protocolo ou ordem de serviço' })
  @ApiQuery({ name: 'status', required: true, description: 'Filtrar por status' })
  getDelivery(@Query('protocoloOrdemServico') protocoloOrdemServico: string,
      @Query('status') status: string) {
    return this.gestaowebService.getDelivery(protocoloOrdemServico, status);
  }

}
