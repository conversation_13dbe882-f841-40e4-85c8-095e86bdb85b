import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { IntegrationConfigService } from '../../common/services/integration-config.service';
import { CreateProtocoloDto } from '../dto/create-protocolo.dto';
import { ProtocoloDto, ProtocoloProdutoDto } from '../dto/protocolo.dto';
import { 
  ExternalProtocoloRequestDto, 
  ExternalProtocoloCreateResponseDto,
  ExternalProtocoloResponseDto,
  ExternalAnexoDto,
  ExternalProtocoloProdutoDto
} from '../dto/external-api.dto';

@Injectable()
export class ProtocoloIntegrationService {
  private readonly logger = new Logger(ProtocoloIntegrationService.name);

  constructor(private readonly configService: IntegrationConfigService) {}

  async createProtocolo(createProtocoloDto: CreateProtocoloDto): Promise<ProtocoloDto> {
    try {
      const externalDto = this.mapToExternalRequest(createProtocoloDto);
      const headers = this.configService.getIntegrationHeaders();
      
      this.logger.log(`Criando protocolo na API externa para cliente: ${createProtocoloDto.nome}`);

      const response: AxiosResponse<ExternalProtocoloCreateResponseDto> = await axios.post(
        'https://api2.telecontrol.com.br/api-callcenter',
        externalDto,
        { headers }
      );

      const protocoloId = response.data.data[0].id;
      return await this.findProtocoloById(protocoloId);
    } catch (error) {
      this.logger.error(`Erro ao criar protocolo: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao criar protocolo');
    }
  }

  async findProtocoloById(id: number): Promise<ProtocoloDto> {
    try {
      const headers = this.configService.getIntegrationHeaders();

      this.logger.log(`Buscando protocolo na API externa: ID ${id}`);

      const response: AxiosResponse<ExternalProtocoloResponseDto> = await axios.get(
        `https://api2.telecontrol.com.br/api-callcenter/${id}`,
        { headers }
      );

      return this.mapToProtocoloDto(response.data);
    } catch (error) {
      this.logger.error(`Erro ao buscar protocolo: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar protocolo');
    }
  }

  async findProtocolosByCpf(cpf: string): Promise<ProtocoloDto[]> {
    try {
      const headers = this.configService.getIntegrationHeaders();

      this.logger.log(`Buscando protocolos na API externa por CPF: ${cpf}`);

      const response: AxiosResponse<ExternalProtocoloResponseDto> = await axios.get(
        `https://api2.telecontrol.com.br/api-callcenter/callcenter/cpf/${cpf}`,
        { headers }
      );

      return response.data.data.map(item => this.mapSingleProtocoloToDto(item));
    } catch (error) {
      this.logger.error(`Erro ao buscar protocolos por CPF: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar protocolos por CPF');
    }
  }

  async findProtocolosByPeriod(dataInicial: string, dataFinal: string): Promise<ProtocoloDto[]> {
    try {
      const headers = this.configService.getIntegrationHeaders();

      this.logger.log(`Buscando protocolos na API externa por período: ${dataInicial} a ${dataFinal}`);

      const response: AxiosResponse<ExternalProtocoloResponseDto> = await axios.get(
        `https://api2.telecontrol.com.br/api-callcenter/dataInicial/${dataInicial}/dataFinal/${dataFinal}`,
        { headers }
      );

      return response.data.data.map(item => this.mapSingleProtocoloToDto(item));
    } catch (error) {
      this.logger.error(`Erro ao buscar protocolos por período: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar protocolos por período');
    }
  }

  private mapToExternalRequest(dto: CreateProtocoloDto): ExternalProtocoloRequestDto {
    const anexos: ExternalAnexoDto[] = dto.anexos?.map(anexo => ({
      descricao: anexo.descricao,
      arquivo: anexo.arquivo,
    })) || [];

    const produtos: ExternalProtocoloProdutoDto[] = dto.produtos.map(produto => ({
      referencia: produto.referencia,
      voltagem: produto.voltagem,
      nota_fiscal: produto.nota_fiscal,
      data_nf: produto.data_nf,
      defeito_reclamado: produto.defeito_reclamado,
      os: produto.os,
    }));

    return {
      nome: dto.nome,
      cpf: this.cleanCpf(dto.cpf),
      email: dto.email,
      celular: this.cleanPhone(dto.celular),
      fone: this.cleanPhone(dto.fone),
      cep: this.cleanCep(dto.cep),
      endereco: dto.endereco,
      numero: dto.numero,
      origem: dto.origem,
      classificacao: dto.classificacao,
      anexos,
      produtos,
      horario: dto.horario,
    };
  }

  private mapToProtocoloDto(externalDto: ExternalProtocoloResponseDto): ProtocoloDto {
    const item = externalDto.data[0];
    return this.mapSingleProtocoloToDto(item);
  }

  private mapSingleProtocoloToDto(item: any): ProtocoloDto {
    const produtos: ProtocoloProdutoDto[] = item.attributes.produtos?.map((produto: any) => ({
      referencia: produto.referencia,
      descricao: produto.descricao,
      qtde: produto.qtde,
      serie: produto.serie,
      defeito_reclamado: produto.defeito_reclamado,
      posto: produto.posto,
      os: produto.os,
    })) || [];

    const protocoloDto = new ProtocoloDto();
    protocoloDto.id = item.id;
    protocoloDto.protocolo = item.attributes.protocolo;
    protocoloDto.dataAbertura = item.attributes.dataAbertura;
    protocoloDto.classificacao = item.attributes.classificacao;
    protocoloDto.providencia = item.attributes.providencia;
    protocoloDto.origem = item.attributes.origem;
    protocoloDto.situacao = item.attributes.situacao;
    protocoloDto.produtos = produtos;

    return protocoloDto;
  }

  private cleanCpf(cpf: string): string {
    return cpf?.replace(/\D/g, '') || '';
  }

  private cleanCep(cep: string): string {
    return cep?.replace(/\D/g, '') || '';
  }

  private cleanPhone(phone: string): string {
    return phone?.replace(/\D/g, '') || '';
  }

  private handleApiError(error: any, message: string): never {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      if (status === 404) {
        throw new HttpException('Protocolo não encontrado', HttpStatus.NOT_FOUND);
      }
      
      throw new HttpException(
        `${message}: ${data?.message || error.message}`,
        status >= 500 ? HttpStatus.INTERNAL_SERVER_ERROR : HttpStatus.BAD_REQUEST
      );
    }
    
    throw new HttpException(
      `${message}: ${error.message}`,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}
