import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne } from 'typeorm';
import { Protocolo } from './protocolo.entity';

@Entity()
export class Status {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  status: string;

  @Column()
  data: string;

  @Column()
  hora: string;

  @Column()
  observacao: string;

  @ManyToOne(() => Protocolo, protocolo => protocolo.historicoStatus)
  protocolo: Protocolo;
}