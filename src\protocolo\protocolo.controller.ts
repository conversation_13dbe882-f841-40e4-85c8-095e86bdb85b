import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ProtocoloService } from './protocolo.service';
import { CreateProtocoloDto } from './dto/create-protocolo.dto';
import { ProtocoloDto } from './dto/protocolo.dto';

@ApiTags('protocolos')
@Controller('protocolos')
export class ProtocoloController {
  constructor(private readonly protocoloService: ProtocoloService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo protocolo' })
  @ApiResponse({ status: 201, description: 'Protocolo criado com sucesso', type: ProtocoloDto })
  create(@Body() createProtocoloDto: CreateProtocoloDto): Promise<ProtocoloDto> {
    return this.protocoloService.create(createProtocoloDto);
  }

  @Get()
  @ApiOperation({ summary: 'Buscar protocolos por período' })
  @ApiQuery({ name: 'dataInicial', required: true, description: 'Data inicial (yyyy-MM-dd)' })
  @ApiQuery({ name: 'dataFinal', required: true, description: 'Data final (yyyy-MM-dd)' })
  @ApiResponse({ status: 200, description: 'Lista de protocolos retornada com sucesso' })
  findByPeriod(
    @Query('dataInicial') dataInicial: string,
    @Query('dataFinal') dataFinal: string,
  ): Promise<ProtocoloDto[]> {
    return this.protocoloService.findByPeriod(dataInicial, dataFinal);
  }

  @Get('cpf/:cpf')
  @ApiOperation({ summary: 'Buscar protocolos por CPF' })
  @ApiParam({ name: 'cpf', description: 'CPF do cliente (apenas números)' })
  @ApiResponse({ status: 200, description: 'Lista de protocolos retornada com sucesso' })
  findByCpf(@Param('cpf') cpf: string): Promise<ProtocoloDto[]> {
    return this.protocoloService.findByCpf(cpf);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar um protocolo pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do protocolo' })
  @ApiResponse({ status: 200, description: 'Protocolo encontrado', type: ProtocoloDto })
  @ApiResponse({ status: 404, description: 'Protocolo não encontrado' })
  findOne(@Param('id') id: string): Promise<ProtocoloDto> {
    return this.protocoloService.findOne(+id);
  }
}
