# Nova Funcionalidade - Processamento de Notas Fiscais

## Visão Geral

O endpoint de produtos foi completamente reformulado para processar notas fiscais via OCR e realizar uma série de validações e buscas automatizadas.

## Fluxo de Funcionamento

### 1. Entrada
- **Endpoint**: `POST /produtos`
- **Payload**:
  ```json
  {
    "notaFiscalBase64": "iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAVORK5CYII=",
    "cep": "01234567",
    "descricao": "Autoclave Amora" // opcional
  }
  ```

### 2. Processamento

#### 2.1 OCR da Nota Fiscal
- Processa a imagem em base64 usando Tesseract.js
- Extrai:
  - Data de emissão
  - Lista de itens/produtos
  - CEP (se disponível)

#### 2.2 Validação de Garantia
- Verifica se a data de emissão está dentro de 6 meses
- Se não estiver válida, retorna erro de garantia

#### 2.3 Comparação de Produtos
- Busca todos os produtos do catálogo
- Compara itens da nota fiscal com produtos usando similaridade de texto
- Considera descrição opcional se fornecida
- Threshold de similaridade: 60%

#### 2.4 Busca de Postos
- Se produtos forem encontrados, busca postos próximos pelo CEP
- Usa novo endpoint: `/institucional/postoMaisProximo/cep/{cep}`

#### 2.5 Preparação para Protocolo
- Se não encontrar postos, prepara dados para criação de protocolo

### 3. Respostas Possíveis

#### 3.1 Postos Encontrados
```json
{
  "key": "POSTOS",
  "data": [
    {
      "nome": "PIXELS ELETRONICA LTDA",
      "contato_endereco": "Avenida Vasco da Gama",
      "contato_numero": "241",
      "contato_complemento": "B",
      "contato_bairro": "Federação",
      "contato_cidade": "SALVADOR",
      "contato_email": "<EMAIL>",
      "fone": "(71)3245-0226",
      "codigo_posto": "28739322000176",
      "distance": "2.5714702971166683"
    }
  ]
}
```

#### 3.2 Garantia Não Válida
```json
{
  "key": "NAO_VALIDA",
  "data": {
    "message": "Garantia não é válida",
    "expired": "15-01-2024"
  }
}
```

#### 3.3 Produto Não Encontrado
```json
{
  "key": "NAO_ENCONTRADO",
  "data": {
    "message": "Produto não encontrado na nota fiscal"
  }
}
```

#### 3.4 Protocolo Preparado
```json
{
  "key": "PROTOCOLO",
  "data": {
    "message": "Protocolo preparado para criação",
    "protocolo": "PENDING"
  }
}
```

## Arquivos Criados/Modificados

### Novos Arquivos
- `src/produto/dto/process-nota-fiscal.dto.ts` - DTO de entrada
- `src/produto/dto/process-response.dto.ts` - DTOs de resposta
- `src/produto/services/ocr.service.ts` - Serviço de OCR
- `src/produto/services/garantia-validation.service.ts` - Validação de garantia
- `src/produto/services/produto-comparison.service.ts` - Comparação de produtos
- `src/produto/services/nota-fiscal-processor.service.ts` - Orquestrador principal

### Arquivos Modificados
- `src/produto/produto.controller.ts` - Novo endpoint
- `src/produto/produto.module.ts` - Novos providers
- `src/posto/dto/posto.dto.ts` - Campos adicionais
- `src/posto/services/posto-integration.service.ts` - Novo método sem linha
- `src/posto/posto.service.ts` - Novo método
- `package.json` - Dependências: tesseract.js, string-similarity, multer

## Dependências Adicionadas
- `tesseract.js` - OCR
- `string-similarity` - Comparação de texto
- `multer` - Upload de arquivos
- `@types/multer` - Tipos TypeScript

## Configuração

### Variáveis de Ambiente
As mesmas variáveis de ambiente existentes são utilizadas para as integrações com a API telecontrol.

### Headers de Integração
- `access-application-key`
- `access-env`

## Limitações e Considerações

1. **OCR**: A precisão depende da qualidade da imagem da nota fiscal
2. **Similaridade**: Threshold de 60% pode precisar ajustes conforme uso
3. **Performance**: OCR pode ser lento para imagens grandes
4. **Protocolo**: Implementação de criação de protocolo está preparada mas não implementada

## Próximos Passos

1. Implementar criação real de protocolo quando não há postos
2. Ajustar threshold de similaridade baseado em testes
3. Otimizar performance do OCR
4. Adicionar cache para produtos do catálogo
5. Implementar logs detalhados para debugging
