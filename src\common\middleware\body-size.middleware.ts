import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class BodySizeMiddleware implements NestMiddleware {
  private readonly logger = new Logger(BodySizeMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    // Log do tamanho do body para debugging
    if (req.body && typeof req.body === 'object') {
      const bodySize = JSON.stringify(req.body).length;
      const bodySizeMB = (bodySize / (1024 * 1024)).toFixed(2);
      
      this.logger.log(`Request body size: ${bodySizeMB}MB`);
      
      // Aviso se o body estiver muito grande
      if (bodySize > 40 * 1024 * 1024) { // 40MB
        this.logger.warn(`Large request body detected: ${bodySizeMB}MB`);
      }
    }
    
    next();
  }
}
