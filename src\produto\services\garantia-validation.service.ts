import { Injectable, Logger } from '@nestjs/common';

export interface GarantiaValidationResult {
  isValid: boolean;
  dataExpiracao?: string;
  message?: string;
}

@Injectable()
export class GarantiaValidationService {
  private readonly logger = new Logger(GarantiaValidationService.name);

  validateGarantia(dataEmissao: Date): GarantiaValidationResult {
    try {
      if (!dataEmissao) {
        this.logger.warn('Data de emissão não encontrada na nota fiscal');
        return {
          isValid: false,
          message: 'Data de emissão não encontrada na nota fiscal'
        };
      }

      const dataAtual = new Date();
      const dataLimite = new Date(dataEmissao);
      
      // Adiciona 6 meses à data de emissão
      dataLimite.setMonth(dataLimite.getMonth() + 6);

      const isValid = dataAtual <= dataLimite;

      if (!isValid) {
        const dataExpiracaoFormatada = this.formatDate(dataLimite);
        this.logger.log(`Garantia expirada. Data limite: ${dataExpiracaoFormatada}`);
        
        return {
          isValid: false,
          dataExpiracao: dataExpiracaoFormatada,
          message: 'Garantia não é válida'
        };
      }

      this.logger.log('Garantia válida');
      return {
        isValid: true,
        message: 'Garantia válida'
      };

    } catch (error) {
      this.logger.error(`Erro ao validar garantia: ${error.message}`, error.stack);
      return {
        isValid: false,
        message: 'Erro ao validar garantia'
      };
    }
  }

  private formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}-${month}-${year}`;
  }

  calculateRemainingDays(dataEmissao: Date): number {
    if (!dataEmissao) return 0;

    const dataAtual = new Date();
    const dataLimite = new Date(dataEmissao);
    dataLimite.setMonth(dataLimite.getMonth() + 6);

    const diffTime = dataLimite.getTime() - dataAtual.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(0, diffDays);
  }
}
