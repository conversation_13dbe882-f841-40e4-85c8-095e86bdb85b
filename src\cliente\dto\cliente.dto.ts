import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class ClienteDto {
  @ApiProperty({ example: 1, description: 'ID do cliente' })
  id: number;

  @ApiProperty({ example: '<PERSON>', description: 'Nome do cliente' })
  nome: string;

  @ApiProperty({ example: '123.456.789-00', description: 'CPF do cliente', required: false })
  cpf?: string;



  @ApiProperty({ example: '<EMAIL>', description: 'Email do cliente' })
  email: string;

  @ApiProperty({ example: '(11) 98765-4321', description: 'Telefone do cliente' })
  telefone: string;

  @ApiProperty({ example: '01234-567', description: 'CEP' })
  cep: string;

  @ApiProperty({ example: '123', description: 'Número do endereço' })
  numero: string;

  @ApiProperty({ example: 'Apto 101', description: 'Complemento do endereço', required: false })
  complemento?: string;

  @ApiProperty({ example: 'Centro', description: 'Bairro' })
  bairro: string;

  @ApiProperty({ example: 'São Paulo', description: 'Cidade' })
  cidade: string;

  @ApiProperty({ example: 'SP', description: 'Estado (UF)' })
  uf: string;
}