import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsBoolean } from 'class-validator';

export class CreateProdutoDto {
  @ApiProperty({ example: 'AM104INLIPTN', description: 'Referência do produto' })
  @IsNotEmpty()
  @IsString()
  referencia: string;

  @ApiProperty({ example: 'AUTOCLAVE AMORA 04 INOX 127-220V LILAS PORTUGUES N', description: 'Descrição do produto' })
  @IsNotEmpty()
  @IsString()
  descricao: string;

  @ApiProperty({ example: '01', description: 'Código da família' })
  @IsNotEmpty()
  @IsString()
  codigoFamilia: string;

  @ApiProperty({ example: '01', description: 'Código da linha' })
  @IsNotEmpty()
  @IsString()
  codigoLinha: string;

  @ApiProperty({ example: '12', description: 'Garantia em meses' })
  @IsNotEmpty()
  @IsString()
  garantia: string;

  @ApiProperty({ example: '0', description: 'Mão de obra', required: false })
  @IsOptional()
  @IsString()
  maoDeObra?: string;

  @ApiProperty({ example: '0', description: 'Mão de obra admin', required: false })
  @IsOptional()
  @IsString()
  maoDeObraAdmin?: string;

  @ApiProperty({ example: true, description: 'Número de série obrigatório', required: false })
  @IsOptional()
  @IsBoolean()
  numeroSerieObrigatorio?: boolean;

  @ApiProperty({ example: true, description: 'Produto ativo', required: false })
  @IsOptional()
  @IsBoolean()
  ativo?: boolean;
}

