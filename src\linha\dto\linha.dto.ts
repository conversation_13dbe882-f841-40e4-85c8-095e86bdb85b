import { ApiProperty } from '@nestjs/swagger';

export class FabricaDto {
  @ApiProperty({ example: 1, description: 'Código da fábrica' })
  fabrica: number;

  @ApiProperty({ example: 'Teste', description: 'Nome da fábrica' })
  nome: string;
}

export class LinhaDto {
  @ApiProperty({ example: 1, description: 'Código da linha' })
  linha: number;

  @ApiProperty({ type: FabricaDto, description: 'Dados da fábrica' })
  fabrica: FabricaDto;

  @ApiProperty({ example: 'AIOTESTE', description: 'Nome da linha' })
  nome: string;

  @ApiProperty({ example: '01', description: 'Código da linha' })
  codigoLinha: string;

  @ApiProperty({ example: true, description: 'Status ativo' })
  ativo: boolean;
}
