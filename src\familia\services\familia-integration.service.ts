import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { IntegrationConfigService } from '../../common/services/integration-config.service';
import { FamiliaDto } from '../dto/familia.dto';

@Injectable()
export class FamiliaIntegrationService {
  private readonly logger = new Logger(FamiliaIntegrationService.name);

  constructor(private readonly configService: IntegrationConfigService) {}

  async findAllFamilias(): Promise<FamiliaDto[]> {
    try {
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log('Buscando todas as famílias na API externa');

      const response: AxiosResponse<FamiliaDto[]> = await axios.get(
        `${baseUrl}/familias`,
        { headers }
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Erro ao buscar famílias: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar famílias');
    }
  }

  async findFamiliaByCodigo(codigo: string): Promise<FamiliaDto> {
    try {
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log(`Buscando família na API externa: código ${codigo}`);

      const response: AxiosResponse<FamiliaDto> = await axios.get(
        `${baseUrl}/familias/codigo/${codigo}`,
        { headers }
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Erro ao buscar família: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar família');
    }
  }

  private handleApiError(error: any, message: string): never {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      if (status === 404) {
        throw new HttpException('Família não encontrada', HttpStatus.NOT_FOUND);
      }
      
      throw new HttpException(
        `${message}: ${data?.message || error.message}`,
        status >= 500 ? HttpStatus.INTERNAL_SERVER_ERROR : HttpStatus.BAD_REQUEST
      );
    }
    
    throw new HttpException(
      `${message}: ${error.message}`,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}
