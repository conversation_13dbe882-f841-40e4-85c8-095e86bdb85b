import { ApiProperty } from '@nestjs/swagger';

export class ProtocoloProdutoDto {
  @ApiProperty({ example: 'ABCD', description: 'Referência do produto' })
  referencia: string;

  @ApiProperty({ example: 'Produto 1', description: 'Descrição do produto' })
  descricao: string;

  @ApiProperty({ example: '1', description: 'Quantidade' })
  qtde: string;

  @ApiProperty({ example: '54615646', description: 'Número de série' })
  serie: string;

  @ApiProperty({ example: 'Não aquece', description: 'Defeito reclamado' })
  defeito_reclamado: string;

  @ApiProperty({ example: 'PA001 - Posto indicado', description: 'Posto' })
  posto: string;

  @ApiProperty({ example: '234567', description: 'Número da OS' })
  os: string;
}

export class ProtocoloDto {
  @ApiProperty({ example: 12345, description: 'ID do protocolo' })
  id: number;

  @ApiProperty({ example: 12345, description: 'Número do protocolo' })
  protocolo: number;

  @ApiProperty({ example: '2020-03-05', description: 'Data de abertura' })
  dataAbertura: string;

  @ApiProperty({ example: 'BACKOFFICE', description: 'Classificação do protocolo' })
  classificacao: string;

  @ApiProperty({ example: 'Aguardando Análise', description: 'Providência' })
  providencia: string;

  @ApiProperty({ example: 'Telefone', description: 'Origem do protocolo' })
  origem: string;

  @ApiProperty({ example: 'Aberto', description: 'Situação do protocolo' })
  situacao: string;

  @ApiProperty({ type: [ProtocoloProdutoDto], description: 'Lista de produtos do protocolo' })
  produtos: ProtocoloProdutoDto[];
}