import { ApiProperty } from '@nestjs/swagger';

export class PostoDto {
  @ApiProperty({ example: 'PIXELS ELETRONICA LTDA', description: 'Nome do posto' })
  nome: string;

  @ApiProperty({ example: 'Avenida Vasco da Gama', description: 'Endereço do posto' })
  contato_endereco: string;

  @ApiProperty({ example: '241', description: 'Número do endereço' })
  contato_numero: string;

  @ApiProperty({ example: 'B', description: 'Complemento do endereço' })
  contato_complemento: string;

  @ApiProperty({ example: 'Federação', description: 'Bairro' })
  contato_bairro: string;

  @ApiProperty({ example: 'SALVADOR', description: 'Cidade' })
  contato_cidade: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email do posto' })
  contato_email: string;

  @ApiProperty({ example: '(71)3245-0226', description: 'Telefone do posto' })
  fone: string;

  @ApiProperty({ example: '28739322000176', description: 'Código do posto' })
  codigo_posto: string;

  @ApiProperty({ example: '2.5714702971166683', description: 'Distância em km' })
  distance: string;
}
