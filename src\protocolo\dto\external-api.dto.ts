import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO para anexos no protocolo
 */
export class ExternalAnexoDto {
  @ApiProperty({ example: 'teste.png', description: 'Descrição do anexo' })
  descricao: string;

  @ApiProperty({ example: 'iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAVORK5CYII=', description: 'Arquivo em base64' })
  arquivo: string;
}

/**
 * DTO para produtos no protocolo
 */
export class ExternalProtocoloProdutoDto {
  @ApiProperty({ example: '6426-01', description: 'Referência do produto' })
  referencia: string;

  @ApiProperty({ example: '220V', description: 'Voltagem do produto' })
  voltagem: string;

  @ApiProperty({ example: '321654', description: 'Número da nota fiscal' })
  nota_fiscal: string;

  @ApiProperty({ example: '2020-01-05', description: 'Data da nota fiscal' })
  data_nf: string;

  @ApiProperty({ example: 'Não Liga', description: 'Defeito reclamado' })
  defeito_reclamado: string;

  @ApiProperty({ example: '54691217', description: 'Número da OS' })
  os: string;
}

/**
 * DTO para enviar dados para a API externa (POST)
 */
export class ExternalProtocoloRequestDto {
  @ApiProperty({ example: 'Ronald Santos', description: 'Nome do cliente' })
  nome: string;

  @ApiProperty({ example: '3215646400', description: 'CPF do cliente (apenas números)' })
  cpf: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email do cliente' })
  email: string;

  @ApiProperty({ example: '99999999999', description: 'Celular do cliente' })
  celular: string;

  @ApiProperty({ example: '99999999999', description: 'Telefone do cliente' })
  fone: string;

  @ApiProperty({ example: '17519255', description: 'CEP (apenas números)' })
  cep: string;

  @ApiProperty({ example: 'Rua A', description: 'Endereço' })
  endereco: string;

  @ApiProperty({ example: '100', description: 'Número do endereço' })
  numero: string;

  @ApiProperty({ example: 'CHAT', description: 'Origem do protocolo' })
  origem: string;

  @ApiProperty({ example: 'BACKOFFICE', description: 'Classificação do protocolo' })
  classificacao: string;

  @ApiProperty({ type: [ExternalAnexoDto], description: 'Lista de anexos' })
  anexos: ExternalAnexoDto[];

  @ApiProperty({ type: [ExternalProtocoloProdutoDto], description: 'Lista de produtos' })
  produtos: ExternalProtocoloProdutoDto[];

  @ApiProperty({ example: '16:30', description: 'Horário' })
  horario: string;
}

/**
 * DTO para resposta de criação da API externa
 */
export class ExternalProtocoloCreateResponseDto {
  @ApiProperty({ description: 'Metadados da resposta' })
  meta: {
    result: string;
    status: string;
  };

  @ApiProperty({ description: 'Dados do protocolo criado' })
  data: Array<{
    type: string;
    id: number;
  }>;

  @ApiProperty({ description: 'Links relacionados' })
  links: {
    self: string;
  };
}

/**
 * DTO para produto na resposta de busca
 */
export class ExternalProtocoloProdutoResponseDto {
  @ApiProperty({ example: 'ABCD', description: 'Referência do produto' })
  referencia: string;

  @ApiProperty({ example: 'Produto 1', description: 'Descrição do produto' })
  descricao: string;

  @ApiProperty({ example: '1', description: 'Quantidade' })
  qtde: string;

  @ApiProperty({ example: '54615646', description: 'Número de série' })
  serie: string;

  @ApiProperty({ example: 'Não aquece', description: 'Defeito reclamado' })
  defeito_reclamado: string;

  @ApiProperty({ example: 'PA001 - Posto indicado', description: 'Posto' })
  posto: string;

  @ApiProperty({ example: '234567', description: 'Número da OS' })
  os: string;
}

/**
 * DTO para resposta de busca da API externa
 */
export class ExternalProtocoloResponseDto {
  @ApiProperty({ description: 'Metadados da resposta' })
  meta: {
    result: string;
    status: number;
    totalResults: number;
    totalPerPage: number;
    totalPages: number;
    currentPage: number;
  };

  @ApiProperty({ description: 'Dados do protocolo' })
  data: Array<{
    type: string;
    id: number;
    attributes: {
      protocolo: number;
      dataAbertura: string;
      classificacao: string;
      providencia: string;
      origem: string;
      situacao: string;
      produtos: ExternalProtocoloProdutoResponseDto[];
    };
  }>;

  @ApiProperty({ description: 'Links relacionados' })
  links: {
    self: string;
  };
}
