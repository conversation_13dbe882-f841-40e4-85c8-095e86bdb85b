import { Injectable } from '@nestjs/common';
import { FamiliaIntegrationService } from './services/familia-integration.service';
import { FamiliaDto } from './dto/familia.dto';

@Injectable()
export class FamiliaService {
  constructor(
    private readonly familiaIntegrationService: FamiliaIntegrationService,
  ) {}

  async findAll(): Promise<FamiliaDto[]> {
    return await this.familiaIntegrationService.findAllFamilias();
  }

  async findByCodigo(codigo: string): Promise<FamiliaDto> {
    return await this.familiaIntegrationService.findFamiliaByCodigo(codigo);
  }
}
