import { ApiProperty } from '@nestjs/swagger';

export class ProdutoDto {
  @ApiProperty({ example: 408790, description: 'ID do produto' })
  id: number;

  @ApiProperty({ example: 'AM104INLIPTN', description: 'Referência do produto' })
  referencia: string;

  @ApiProperty({ example: 'AUTOCLAVE AMORA 04 INOX 127-220V LILAS PORTUGUES N', description: 'Descrição do produto' })
  descricao: string;

  @ApiProperty({ example: 24, description: 'Garantia em meses' })
  garantia: number;

  @ApiProperty({ example: true, description: 'Produto ativo' })
  ativo: boolean;

  @ApiProperty({ example: 'AUTOCLAVE AMORA', description: 'Descrição da família' })
  familia: string;

  @ApiProperty({ example: 'BIOSEGUERANÇA', description: 'Nome da linha' })
  linha: string;

  @ApiProperty({ example: 'BIVOLT', description: 'Voltagem do produto', required: false })
  voltagem?: string;

  @ApiProperty({ example: 'NAC', description: 'Origem do produto' })
  origem: string;
}

