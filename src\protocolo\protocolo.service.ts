import { Injectable } from '@nestjs/common';
import { CreateProtocoloDto } from './dto/create-protocolo.dto';
import { ProtocoloDto } from './dto/protocolo.dto';
import { ProtocoloIntegrationService } from './services/protocolo-integration.service';

@Injectable()
export class ProtocoloService {
  constructor(
    private readonly protocoloIntegrationService: ProtocoloIntegrationService,
  ) {}

  async create(createProtocoloDto: CreateProtocoloDto): Promise<ProtocoloDto> {
    return await this.protocoloIntegrationService.createProtocolo(createProtocoloDto);
  }

  async findByCpf(cpf: string): Promise<ProtocoloDto[]> {
    return await this.protocoloIntegrationService.findProtocolosByCpf(cpf);
  }

  async findByPeriod(dataInicial: string, dataFinal: string): Promise<ProtocoloDto[]> {
    return await this.protocoloIntegrationService.findProtocolosByPeriod(dataInicial, dataFinal);
  }

  async findOne(id: number): Promise<ProtocoloDto> {
    return await this.protocoloIntegrationService.findProtocoloById(id);
  }
}

