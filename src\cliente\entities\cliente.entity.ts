import { <PERSON>tity, Column, PrimaryGeneratedC<PERSON>umn, OneToMany } from 'typeorm';
import { Protocolo } from '../../protocolo/entities/protocolo.entity';

@Entity()
export class Cliente {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  nome: string;

  @Column({ nullable: true })
  cpf: string;

  @Column({ nullable: true })
  cnpj: string;

  @Column()
  email: string;

  @Column()
  telefone: string;

  @Column()
  cep: string;

  @Column()
  numero: string;

  @Column({ nullable: true })
  complemento: string;

  @Column()
  bairro: string;

  @Column()
  cidade: string;

  @Column()
  uf: string;

  @OneToMany(() => Protocolo, protocolo => protocolo.cliente)
  protocolos: Protocolo[];
}
