import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { LinhaService } from './linha.service';
import { LinhaDto } from './dto/linha.dto';

@ApiTags('linhas')
@Controller('linhas')
export class LinhaController {
  constructor(private readonly linhaService: LinhaService) {}

  @Get()
  @ApiOperation({ summary: 'Listar todas as linhas' })
  @ApiResponse({ status: 200, description: 'Lista de linhas retornada com sucesso', type: [LinhaDto] })
  findAll(): Promise<LinhaDto[]> {
    return this.linhaService.findAll();
  }

  @Get('codigo/:codigo')
  @ApiOperation({ summary: 'Buscar uma linha pelo código' })
  @ApiParam({ name: 'codigo', description: '<PERSON><PERSON><PERSON> da linha' })
  @ApiResponse({ status: 200, description: 'Linha encontrada', type: LinhaDto })
  @ApiResponse({ status: 404, description: 'Linha não encontrada' })
  findByCodigoLinha(@Param('codigo') codigo: string): Promise<LinhaDto> {
    return this.linhaService.findByCodigoLinha(codigo);
  }
}
