import { Injectable } from '@nestjs/common';
import { PostoIntegrationService } from './services/posto-integration.service';
import { PostoDto } from './dto/posto.dto';

@Injectable()
export class PostoService {
  constructor(
    private readonly postoIntegrationService: PostoIntegrationService,
  ) {}

  async findPostoMaisProximo(linha: string, cep: string): Promise<PostoDto[]> {
    return await this.postoIntegrationService.findPostoMaisProximo(linha, cep);
  }

  async findPostoMaisProximoByCep(cep: string): Promise<PostoDto[]> {
    return await this.postoIntegrationService.findPostoMaisProximoByCep(cep);
  }
}
