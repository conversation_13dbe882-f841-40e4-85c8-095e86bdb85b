import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ProdutoService } from './produto.service';
import { ProdutoDto } from './dto/produto.dto';
import { ProcessNotaFiscalDto } from './dto/process-nota-fiscal.dto';
import {
  ProcessNotaFiscalResponseDto,
  PostosFoundResponseDto,
  GarantiaNaoValidaResponseDto,
  ProdutoNaoEncontradoResponseDto,
  ProtocoloCriadoResponseDto
} from './dto/process-response.dto';
import { NotaFiscalProcessorService } from './services/nota-fiscal-processor.service';

@ApiTags('produtos')
@Controller('produtos')
export class ProdutoController {
  constructor(
    private readonly produtoService: ProdutoService,
    private readonly notaFiscalProcessorService: NotaFiscalProcessorService
  ) {}

  @Post()
  @ApiOperation({ summary: 'Processar nota fiscal e buscar postos ou criar protocolo' })
  @ApiResponse({ status: 200, description: 'Postos encontrados', type: PostosFoundResponseDto })
  @ApiResponse({ status: 200, description: 'Garantia não válida', type: GarantiaNaoValidaResponseDto })
  @ApiResponse({ status: 200, description: 'Produto não encontrado', type: ProdutoNaoEncontradoResponseDto })
  @ApiResponse({ status: 200, description: 'Protocolo criado', type: ProtocoloCriadoResponseDto })
  processNotaFiscal(@Body() processDto: ProcessNotaFiscalDto): Promise<ProcessNotaFiscalResponseDto> {
    return this.notaFiscalProcessorService.processNotaFiscal(processDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os produtos com filtros' })
  @ApiQuery({ name: 'referencia', required: false, description: 'Filtrar por referência do produto' })
  @ApiResponse({ status: 200, description: 'Lista de produtos retornada com sucesso' })
  findAll(
    @Query('referencia') referencia?: string,
  ): Promise<ProdutoDto[]> {
    return this.produtoService.findAll(referencia);
  }

  @Get(':referencia')
  @ApiOperation({ summary: 'Buscar um produto pela referência' })
  @ApiParam({ name: 'referencia', description: 'Referência do produto' })
  @ApiResponse({ status: 200, description: 'Produto encontrado', type: ProdutoDto })
  @ApiResponse({ status: 404, description: 'Produto não encontrado' })
  findByReferencia(@Param('referencia') referencia: string): Promise<ProdutoDto> {
    return this.produtoService.findByReferencia(referencia);
  }
}

