import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ProdutoService } from './produto.service';
import { CreateProdutoDto } from './dto/create-produto.dto';
import { ProdutoDto } from './dto/produto.dto';

@ApiTags('produtos')
@Controller('produtos')
export class ProdutoController {
  constructor(private readonly produtoService: ProdutoService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo produto' })
  @ApiResponse({ status: 201, description: 'Produto criado com sucesso', type: ProdutoDto })
  create(@Body() createProdutoDto: CreateProdutoDto): Promise<ProdutoDto> {
    return this.produtoService.create(createProdutoDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os produtos com filtros' })
  @ApiQuery({ name: 'referencia', required: false, description: 'Filtrar por referência do produto' })
  @ApiResponse({ status: 200, description: 'Lista de produtos retornada com sucesso' })
  findAll(
    @Query('referencia') referencia?: string,
  ): Promise<ProdutoDto[]> {
    return this.produtoService.findAll(referencia);
  }

  @Get(':referencia')
  @ApiOperation({ summary: 'Buscar um produto pela referência' })
  @ApiParam({ name: 'referencia', description: 'Referência do produto' })
  @ApiResponse({ status: 200, description: 'Produto encontrado', type: ProdutoDto })
  @ApiResponse({ status: 404, description: 'Produto não encontrado' })
  findByReferencia(@Param('referencia') referencia: string): Promise<ProdutoDto> {
    return this.produtoService.findByReferencia(referencia);
  }
}

