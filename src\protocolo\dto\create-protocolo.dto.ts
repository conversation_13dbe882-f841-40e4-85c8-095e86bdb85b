import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsArray, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateProtocoloAnexoDto {
  @ApiProperty({ example: 'teste.png', description: 'Descrição do anexo' })
  @IsNotEmpty()
  @IsString()
  descricao: string;

  @ApiProperty({ example: 'iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAVORK5CYII=', description: 'Arquivo em base64' })
  @IsNotEmpty()
  @IsString()
  arquivo: string;
}

export class CreateProtocoloProdutoDto {
  @ApiProperty({ example: '6426-01', description: 'Referência do produto' })
  @IsNotEmpty()
  @IsString()
  referencia: string;

  @ApiProperty({ example: '220V', description: 'Voltagem do produto' })
  @IsNotEmpty()
  @IsString()
  voltagem: string;

  @ApiProperty({ example: '321654', description: 'Número da nota fiscal' })
  @IsNotEmpty()
  @IsString()
  nota_fiscal: string;

  @ApiProperty({ example: '2020-01-05', description: 'Data da nota fiscal' })
  @IsNotEmpty()
  @IsString()
  data_nf: string;

  @ApiProperty({ example: 'Não Liga', description: 'Defeito reclamado' })
  @IsNotEmpty()
  @IsString()
  defeito_reclamado: string;

  @ApiProperty({ example: '54691217', description: 'Número da OS' })
  @IsNotEmpty()
  @IsString()
  os: string;
}

export class CreateProtocoloDto {
  @ApiProperty({ example: 'Ronald Santos', description: 'Nome do cliente' })
  @IsNotEmpty()
  @IsString()
  nome: string;

  @ApiProperty({ example: '32156464000', description: 'CPF do cliente' })
  @IsNotEmpty()
  @IsString()
  cpf: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email do cliente' })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({ example: '99999999999', description: 'Celular do cliente' })
  @IsNotEmpty()
  @IsString()
  celular: string;

  @ApiProperty({ example: '99999999999', description: 'Telefone do cliente' })
  @IsNotEmpty()
  @IsString()
  fone: string;

  @ApiProperty({ example: '17519255', description: 'CEP' })
  @IsNotEmpty()
  @IsString()
  cep: string;

  @ApiProperty({ example: 'Rua A', description: 'Endereço' })
  @IsNotEmpty()
  @IsString()
  endereco: string;

  @ApiProperty({ example: '100', description: 'Número do endereço' })
  @IsNotEmpty()
  @IsString()
  numero: string;

  @ApiProperty({ example: 'CHAT', description: 'Origem do protocolo' })
  @IsNotEmpty()
  @IsString()
  origem: string;

  @ApiProperty({ example: 'BACKOFFICE', description: 'Classificação do protocolo' })
  @IsNotEmpty()
  @IsString()
  classificacao: string;

  @ApiProperty({ type: [CreateProtocoloAnexoDto], description: 'Lista de anexos', required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateProtocoloAnexoDto)
  anexos?: CreateProtocoloAnexoDto[];

  @ApiProperty({ type: [CreateProtocoloProdutoDto], description: 'Lista de produtos' })
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateProtocoloProdutoDto)
  produtos: CreateProtocoloProdutoDto[];

  @ApiProperty({ example: '16:30', description: 'Horário' })
  @IsNotEmpty()
  @IsString()
  horario: string;
}