import { Injectable } from '@nestjs/common';
import { LinhaIntegrationService } from './services/linha-integration.service';
import { LinhaDto } from './dto/linha.dto';

@Injectable()
export class LinhaService {
  constructor(
    private readonly linhaIntegrationService: LinhaIntegrationService,
  ) {}

  async findAll(): Promise<LinhaDto[]> {
    return await this.linhaIntegrationService.findAllLinhas();
  }

  async findByCodigoLinha(codigo: string): Promise<LinhaDto> {
    return await this.linhaIntegrationService.findLinhaByCodigoLinha(codigo);
  }
}
