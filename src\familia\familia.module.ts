import { Modu<PERSON> } from '@nestjs/common';
import { FamiliaController } from './familia.controller';
import { FamiliaService } from './familia.service';
import { FamiliaIntegrationService } from './services/familia-integration.service';
import { IntegrationConfigService } from '../common/services/integration-config.service';

@Module({
  controllers: [FamiliaController],
  providers: [FamiliaService, FamiliaIntegrationService, IntegrationConfigService],
  exports: [FamiliaService],
})
export class FamiliaModule {}
