import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { PostoService } from './posto.service';
import { PostoDto } from './dto/posto.dto';

@ApiTags('postos')
@Controller('postos')
export class PostoController {
  constructor(private readonly postoService: PostoService) {}

  @Get('mais-proximo/linha/:linha/cep/:cep')
  @ApiOperation({ summary: 'Buscar posto mais próximo por linha e CEP' })
  @ApiParam({ name: 'linha', description: '<PERSON><PERSON><PERSON> da linha' })
  @ApiParam({ name: 'cep', description: 'CEP para busca' })
  @ApiResponse({ status: 200, description: 'Posto mais próximo encontrado', type: [PostoDto] })
  @ApiResponse({ status: 404, description: 'Posto não encontrado' })
  findPostoMaisProximo(
    @Param('linha') linha: string,
    @Param('cep') cep: string,
  ): Promise<PostoDto[]> {
    return this.postoService.findPostoMaisProximo(linha, cep);
  }
}
