import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class GestaowebService {

  async getCollect(protocoloOrdemServico: string, status: string) {

    const uri = `${process.env.GESTAO_WEB_URL}/ObterColetas?ProtocoloOuOrdemServico=${protocoloOrdemServico}&Situacao=${status}`;

    try {
      const response = await axios.get(uri)

      return response.data;

    } catch (error) {
      this.buildError(error);
    }
  }

  async getDelivery(protocoloOrdemServico: string, status: string) {
    const uri = `${process.env.GESTAO_WEB_URL}/ObterEntregas?ProtocoloOuOrdemServico=${protocoloOrdemServico}&Situacao=${status}`;

    try {
      const response = await axios.get(uri)

      return response.data;

    } catch (error) {
      this.buildError(error);
    }

  }

  buildError(error: any) {
    if (axios.isAxiosError(error)) {
      const status = error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
      const message = error.response?.data?.title || error.message || 'Erro na comunicação com o serviço de gestão web';

      throw new HttpException(
        {
          statusCode: status,
          message: message,
        },
        status,
      );
    } else {
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Erro inesperado na comunicação com o serviço de gestão web',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
