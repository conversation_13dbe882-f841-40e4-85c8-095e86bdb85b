import { Test, TestingModule } from '@nestjs/testing';
import { GestaowebController } from './gestaoweb.controller';
import { GestaowebService } from './gestaoweb.service';

describe('GestaowebController', () => {
  let controller: GestaowebController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GestaowebController],
      providers: [GestaowebService],
    }).compile();

    controller = module.get<GestaowebController>(GestaowebController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
