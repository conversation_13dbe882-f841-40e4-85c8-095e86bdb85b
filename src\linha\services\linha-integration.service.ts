import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { IntegrationConfigService } from '../../common/services/integration-config.service';
import { LinhaDto } from '../dto/linha.dto';

@Injectable()
export class LinhaIntegrationService {
  private readonly logger = new Logger(LinhaIntegrationService.name);

  constructor(private readonly configService: IntegrationConfigService) {}

  async findAllLinhas(): Promise<LinhaDto[]> {
    try {
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log('Buscando todas as linhas na API externa');

      const response: AxiosResponse<LinhaDto[]> = await axios.get(
        `${baseUrl}/linhas`,
        { headers }
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Erro ao buscar linhas: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar linhas');
    }
  }

  async findLinhaByCodigoLinha(codigo: string): Promise<LinhaDto> {
    try {
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log(`Buscando linha na API externa: código ${codigo}`);

      const response: AxiosResponse<LinhaDto> = await axios.get(
        `${baseUrl}/linhas/codigoLinha/${codigo}`,
        { headers }
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Erro ao buscar linha: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar linha');
    }
  }

  private handleApiError(error: any, message: string): never {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      if (status === 404) {
        throw new HttpException('Linha não encontrada', HttpStatus.NOT_FOUND);
      }
      
      throw new HttpException(
        `${message}: ${data?.message || error.message}`,
        status >= 500 ? HttpStatus.INTERNAL_SERVER_ERROR : HttpStatus.BAD_REQUEST
      );
    }
    
    throw new HttpException(
      `${message}: ${error.message}`,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}
