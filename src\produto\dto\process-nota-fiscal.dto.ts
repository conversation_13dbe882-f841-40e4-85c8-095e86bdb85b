import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class ProcessNotaFiscalDto {
  @ApiProperty({ 
    example: 'iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAVORK5CYII=', 
    description: 'Nota fiscal em formato base64' 
  })
  @IsNotEmpty()
  @IsString()
  notaFiscalBase64: string;

  @ApiProperty({ 
    example: '01234567', 
    description: 'CEP para busca de postos próximos' 
  })
  @IsNotEmpty()
  @IsString()
  cep: string;

  @ApiProperty({ 
    example: 'Autoclave Amora', 
    description: 'Descrição opcional do produto', 
    required: false 
  })
  @IsOptional()
  @IsString()
  descricao?: string;
}
