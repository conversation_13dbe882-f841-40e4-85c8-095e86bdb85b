import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO para enviar dados para a API externa (POST/PUT)
 */
export class ExternalClienteRequestDto {
  @ApiProperty({ example: 'teste', description: 'Nome do cliente' })
  nome: string;

  @ApiProperty({ example: '44476592823', description: 'CPF do cliente (apenas números)' })
  cpf: string;

  @ApiProperty({ example: '14056711', description: 'CEP (apenas números)' })
  cep: string;

  @ApiProperty({ example: 'MARILIA', description: 'Cidade' })
  cidade: string;

  @ApiProperty({ example: 'SP', description: 'Estado (UF)' })
  estado: string;

  @ApiProperty({ example: 'rua teste', description: 'Endereço' })
  endereco: string;

  @ApiProperty({ example: '', description: 'Complemento do endereço', required: false })
  complemento?: string;

  @ApiProperty({ example: '145', description: 'Número do endereço' })
  numero: string;

  @ApiProperty({ example: '14991531120', description: 'Telefone (apenas números)' })
  fone: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email do cliente' })
  email: string;

  @ApiProperty({ example: '133131', description: 'Código do cliente' })
  codigoCliente: string;
}

/**
 * DTO para receber dados da API externa (GET)
 */
export class ExternalClienteResponseDto {
  @ApiProperty({ example: 'teste', description: 'Nome do cliente' })
  nome: string;

  @ApiProperty({ example: '44476592817', description: 'CPF do cliente' })
  cpf: string;

  @ApiProperty({ example: 'SP', description: 'Estado (UF)' })
  estado: string;

  @ApiProperty({ example: 'rua teste', description: 'Endereço' })
  endereco: string;

  @ApiProperty({ example: '145', description: 'Número do endereço' })
  numero: string;

  @ApiProperty({ example: '14991531120', description: 'Telefone' })
  fone: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email do cliente' })
  email: string;

  @ApiProperty({ example: '14056711', description: 'CEP' })
  cep: string;

  @ApiProperty({ example: '4124124', description: 'Código do cliente' })
  codigo_cliente: string;
}
