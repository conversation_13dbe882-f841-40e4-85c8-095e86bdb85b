import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { AppService } from './app.service';
import { BodySizeMiddleware } from './common/middleware/body-size.middleware';
import { ClienteModule } from './cliente/cliente.module';
import { ProdutoModule } from './produto/produto.module';
import { ProtocoloModule } from './protocolo/protocolo.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { GestaowebModule } from './gestaoweb/gestaoweb.module';
import { PostoModule } from './posto/posto.module';
import { LinhaModule } from './linha/linha.module';
import { FamiliaModule } from './familia/familia.module';

@Module({
  imports: [
    ConfigModule.forRoot(),
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || '3306'),
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: process.env.NODE_ENV !== 'production',
    }),
    ClienteModule,
    ProdutoModule,
    ProtocoloModule,
    GestaowebModule,
    PostoModule,
    LinhaModule,
    FamiliaModule,
  ],
  controllers: [],
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(BodySizeMiddleware)
      .forRoutes('*'); // Aplica para todas as rotas
  }
}
