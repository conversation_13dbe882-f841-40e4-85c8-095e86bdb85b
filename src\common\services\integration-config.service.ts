import { Injectable } from '@nestjs/common';

export interface IntegrationHeaders {
  'access-application-key': string;
  'access-env': string;
  [key: string]: string;
}

@Injectable()
export class IntegrationConfigService {
  private readonly productionHeaders: IntegrationHeaders = {
    'access-application-key': '0b0b1067fd0ba17a7564bb15a7cc74b1fdf7dd13',
    'access-env': 'PRODUCTION',
  };

  private readonly homologationHeaders: IntegrationHeaders = {
    'access-application-key': '4b4e499d1cd644ba5e4231caf4a949a2b566cbc7',
    'access-env': 'HOMOLOGATION',
  };

  /**
   * Retorna os headers de integração baseado no ambiente atual
   */
  getIntegrationHeaders(): IntegrationHeaders {
    const nodeEnv = process.env.NODE_ENV;
    
    if (nodeEnv === 'production') {
      return this.productionHeaders;
    }
    
    // Por padrão, usa homologação para desenvolvimento e outros ambientes
    return this.homologationHeaders;
  }

  /**
   * Retorna a URL base da API de integração
   */
  getBaseUrl(): string {
    return 'http://api2.telecontrol.com.br/posvenda-core';
  }
}
