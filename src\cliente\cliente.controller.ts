import { Controller, Get, Post, Put, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ClienteService } from './cliente.service';
import { CreateClienteDto } from './dto/create-cliente.dto';
import { UpdateClienteDto } from './dto/update-cliente.dto';
import { ClienteDto } from './dto/cliente.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

@ApiTags('clientes')
@Controller('clientes')
export class ClienteController {
  constructor(private readonly clienteService: ClienteService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo cliente' })
  @ApiResponse({ status: 201, description: 'Cliente criado com sucesso', type: ClienteDto })
  create(@Body() createClienteDto: CreateClienteDto): Promise<ClienteDto> {
    return this.clienteService.create(createClienteDto);
  }

  @Put('/:cpf')
  @ApiOperation({ summary: 'Atualizar um cliente pelo CPF' })
  @ApiParam({ name: 'cpf', description: 'CPF do cliente (apenas números)' })
  @ApiResponse({ status: 200, description: 'Cliente atualizado com sucesso', type: ClienteDto })
  @ApiResponse({ status: 404, description: 'Cliente não encontrado' })
  update(@Param('cpf') cpf: string, @Body() updateClienteDto: UpdateClienteDto): Promise<ClienteDto> {
    return this.clienteService.update(cpf, updateClienteDto);
  }

  @Get('/:cpf')
  @ApiOperation({ summary: 'Buscar um cliente pelo CPF' })
  @ApiParam({ name: 'cpf', description: 'CPF do cliente (apenas números)' })
  @ApiResponse({ status: 200, description: 'Cliente encontrado', type: ClienteDto })
  @ApiResponse({ status: 404, description: 'Cliente não encontrado' })
  findByCpf(@Param('cpf') cpf: string): Promise<ClienteDto> {
    return this.clienteService.findByCpf(cpf);
  }
}
