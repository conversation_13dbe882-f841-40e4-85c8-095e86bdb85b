import { ApiProperty } from '@nestjs/swagger';

export class StatusDto {
  @ApiProperty({ example: 1, description: 'ID do status' })
  id: number;

  @ApiProperty({ 
    example: 'Aberto', 
    description: 'Status do protocolo',
    enum: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Finalizado', 'Cancelado']
  })
  status: string;

  @ApiProperty({ example: '2023-07-15', description: 'Data do status' })
  data: string;

  @ApiProperty({ example: '14:30', description: 'Hora do status' })
  hora: string;

  @ApiProperty({ example: 'Aguardando análise técnica', description: 'Observação sobre o status' })
  observacao: string;
}