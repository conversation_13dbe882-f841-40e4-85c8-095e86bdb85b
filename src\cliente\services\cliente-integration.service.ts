import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { IntegrationConfigService } from '../../common/services/integration-config.service';
import { CreateClienteDto } from '../dto/create-cliente.dto';
import { UpdateClienteDto } from '../dto/update-cliente.dto';
import { ClienteDto } from '../dto/cliente.dto';
import { ExternalClienteRequestDto, ExternalClienteResponseDto } from '../dto/external-api.dto';
import { isValidUF } from '../../common/validators/uf.validator';

@Injectable()
export class ClienteIntegrationService {
  private readonly logger = new Logger(ClienteIntegrationService.name);

  constructor(private readonly configService: IntegrationConfigService) {}

  async createCliente(createClienteDto: CreateClienteDto): Promise<ClienteDto> {
    try {
      // Validação adicional de UF
      if (createClienteDto.uf && !isValidUF(createClienteDto.uf)) {
        throw new HttpException(
          `UF inválida: ${createClienteDto.uf}. Deve ser uma UF válida do Brasil.`,
          HttpStatus.BAD_REQUEST
        );
      }

      const externalDto = this.mapToExternalRequest(createClienteDto);
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log(`Criando cliente na API externa: ${createClienteDto.nome}`);

      const response: AxiosResponse<ExternalClienteResponseDto> = await axios.post(
        `${baseUrl}/clientes`,
        externalDto,
        { headers }
      );

      return this.mapToClienteDto(response.data);
    } catch (error) {
      this.logger.error(`Erro ao criar cliente: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao criar cliente');
    }
  }

  async updateCliente(cpf: string, updateClienteDto: UpdateClienteDto): Promise<ClienteDto> {
    try {
      // Validação adicional de UF
      if (updateClienteDto.uf && !isValidUF(updateClienteDto.uf)) {
        throw new HttpException(
          `UF inválida: ${updateClienteDto.uf}. Deve ser uma UF válida do Brasil.`,
          HttpStatus.BAD_REQUEST
        );
      }

      const externalDto = this.mapUpdateToExternalRequest(updateClienteDto);
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log(`Atualizando cliente na API externa: CPF ${cpf}`);

      const response: AxiosResponse<ExternalClienteResponseDto> = await axios.put(
        `${baseUrl}/clientes/cpf/${cpf}`,
        externalDto,
        { headers }
      );

      return this.mapToClienteDto(response.data);
    } catch (error) {
      this.logger.error(`Erro ao atualizar cliente: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao atualizar cliente');
    }
  }

  async findClienteByCpf(cpf: string): Promise<ClienteDto> {
    try {
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log(`Buscando cliente na API externa: CPF ${cpf}`);

      const response: AxiosResponse<ExternalClienteResponseDto> = await axios.get(
        `${baseUrl}/clientes/cpf/${cpf}`,
        { headers }
      );

      return this.mapToClienteDto(response.data);
    } catch (error) {
      this.logger.error(`Erro ao buscar cliente: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar cliente');
    }
  }

  private mapToExternalRequest(dto: CreateClienteDto): ExternalClienteRequestDto {
    const codigoCliente = this.generateClientCode();
    return {
      nome: dto.nome,
      cpf: this.cleanCpf(dto.cpf || ''),
      cep: this.cleanCep(dto.cep),
      cidade: this.normalizeString(dto.cidade),
      estado: (dto.uf || ''),
      endereco: (dto.endereco || dto.bairro || ''),
      complemento: (dto.complemento || ''),
      numero: dto.numero,
      fone: this.cleanPhone(dto.telefone || dto.fone || ''),
      email: dto.email,
      codigoCliente: codigoCliente,
    };
  }

  private mapUpdateToExternalRequest(dto: UpdateClienteDto): ExternalClienteRequestDto {
    return {
      nome: dto.nome,
      cpf: this.cleanCpf(dto.cpf),
      cep: this.cleanCep(dto.cep),
      cidade: this.normalizeString(dto.cidade),
      estado: dto.uf,
      endereco: `${dto.endereco}`,
      complemento: dto.complemento || '',
      numero: dto.numero,
      fone: this.cleanPhone(dto.telefone),
      email: dto.email,
      codigoCliente: dto.codigoCliente,
    };
  }

  private mapToClienteDto(externalDto: ExternalClienteResponseDto): ClienteDto {
    const clienteDto = new ClienteDto();
    clienteDto.id = parseInt(externalDto.codigo_cliente);
    clienteDto.nome = externalDto.nome;
    clienteDto.cpf = externalDto.cpf;
    clienteDto.email = externalDto.email;
    clienteDto.telefone = externalDto.fone;
    clienteDto.cep = externalDto.cep;
    clienteDto.numero = externalDto.numero;
    clienteDto.complemento = externalDto.complemento || '';
    clienteDto.endereco = externalDto.endereco || '';
    clienteDto.cidade = externalDto.cidade || '';
    clienteDto.uf = externalDto.estado;

    return clienteDto;
  }

  private cleanCpf(cpf: string): string {
    return cpf?.replace(/\D/g, '') || '';
  }

  private cleanCep(cep: string): string {
    return cep?.replace(/\D/g, '') || '';
  }

  private cleanPhone(phone: string): string {
    return phone?.replace(/\D/g, '') || '';
  }

  private generateClientCode(): string {
    return Math.floor(Math.random() * 9999999999).toString().padStart(10, '0');
  }

  private normalizeString(value: string): string {
  return value
    .normalize('NFD') // separa caracteres de seus acentos
    .replace(/[\u0300-\u036f]/g, '') // remove os acentos
    .replace(/ç/g, 'c') // substitui ç por c
    .replace(/[^a-zA-Z0-9\s]/g, ''); // remove outros caracteres especiais, exceto letras, números e espaços
}


  private handleApiError(error: any, message: string): never {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      const responseMessage = data?.message || data?.exception || error.message;

      if (status === 404) {
        if (message.includes('buscar')) {
          throw new HttpException('Cliente não encontrado', HttpStatus.NOT_FOUND);
        } else if (responseMessage?.includes('já cadastrado')) {
          throw new HttpException('Cliente já cadastrado', HttpStatus.CONFLICT);
        } else {
          throw new HttpException(`Endpoint não encontrado: ${message}`, HttpStatus.NOT_FOUND);
        }
      } else if(status === 500) {
        if(message.includes('cidade')) {
          throw new HttpException('Erro interno: cidade inválida', HttpStatus.BAD_REQUEST);
        }
      }

      throw new HttpException(
        `${message}: ${responseMessage}`,
        status >= 500 ? HttpStatus.INTERNAL_SERVER_ERROR : HttpStatus.BAD_REQUEST
      );
    }

    throw new HttpException(
      `${message}: ${error.message}`,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}
