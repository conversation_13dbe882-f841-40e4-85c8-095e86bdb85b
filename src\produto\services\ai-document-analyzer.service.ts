import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import * as pdfParse from 'pdf-parse';

export interface AIAnalysisResult {
  dataEmissao: Date | null;
  itens: string[];
  cep: string | null;
  confianca: number;
  provider: string;
  rawResponse: string;
}

@Injectable()
export class AIDocumentAnalyzerService {
  private readonly logger = new Logger(AIDocumentAnalyzerService.name);

  async analyzeDocument(base64Document: string, tipoDocumento: 'image' | 'pdf'): Promise<AIAnalysisResult> {
    // Tenta diferentes provedores de IA em ordem de preferência
    const providers = [
      { name: 'openai', method: this.analyzeWithOpenAI.bind(this) },
      { name: 'anthropic', method: this.analyzeWithAnthropic.bind(this) },
      { name: 'google', method: this.analyzeWithGoogle.bind(this) }
    ];

    for (const provider of providers) {
      try {
        this.logger.log(`Tentando <PERSON> com ${provider.name}`);
        const result = await provider.method(base64Document, tipoDocumento);
        
        if (result.confianca > 30) { // Threshold mínimo de confiança
          this.logger.log(`Análise bem-sucedida com ${provider.name} (confiança: ${result.confianca}%)`);
          return result;
        }
      } catch (error) {
        this.logger.warn(`Falha na análise com ${provider.name}: ${error.message}`);
        continue;
      }
    }

    // Se todos falharam, retorna resultado básico
    this.logger.error('Todos os provedores de IA falharam');
    return {
      dataEmissao: null,
      itens: [],
      cep: null,
      confianca: 0,
      provider: 'fallback',
      rawResponse: 'Erro: Nenhum provedor de IA disponível'
    };
  }

  private async analyzeWithOpenAI(base64Document: string, tipoDocumento: 'image' | 'pdf'): Promise<AIAnalysisResult> {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY não configurada');
    }

    const prompt = this.buildPrompt();
    const cleanBase64 = base64Document.replace(/^data:[^;]+;base64,/, '');

    let payload: any;

    if (tipoDocumento === 'pdf') {
      // Para PDFs, usa o modelo gpt-4o-mini com texto extraído
      const pdfText = await this.extractTextFromPDF(cleanBase64);

      payload = {
        model: "gpt-4o-mini",
        messages: [
          {
            role: "user",
            content: `${prompt}\n\nTexto extraído do PDF:\n${pdfText}`
          }
        ],
        max_tokens: 1000,
        temperature: 0.1
      };
    } else {
      // Para imagens, usa o modelo gpt-4o-mini com visão
      payload = {
        model: "gpt-4o-mini",
        messages: [
          {
            role: "user",
            content: [
              { type: "text", text: prompt },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/jpeg;base64,${cleanBase64}`,
                  detail: "high"
                }
              }
            ]
          }
        ],
        max_tokens: 1000,
        temperature: 0.1
      };
    }

    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      payload,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const content = response.data.choices[0]?.message?.content || '';
    return this.parseAIResponse(content, 'openai-gpt4o-mini');
  }

  private async extractTextFromPDF(base64PDF: string): Promise<string> {
    try {
      const pdfBuffer = Buffer.from(base64PDF, 'base64');
      const data = await pdfParse(pdfBuffer);
      return data.text || '';
    } catch (error) {
      this.logger.error(`Erro ao extrair texto do PDF: ${error.message}`);
      throw new Error('Erro ao processar PDF');
    }
  }

  private async analyzeWithAnthropic(_base64Document: string, _tipoDocumento: 'image' | 'pdf'): Promise<AIAnalysisResult> {
    const apiKey = process.env.ANTHROPIC_API_KEY;
    if (!apiKey) {
      throw new Error('ANTHROPIC_API_KEY não configurada');
    }

    // Implementação para Claude (Anthropic)
    // Por enquanto, lança erro pois precisa de implementação específica
    throw new Error('Anthropic Claude não implementado ainda');
  }

  private async analyzeWithGoogle(_base64Document: string, _tipoDocumento: 'image' | 'pdf'): Promise<AIAnalysisResult> {
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
      throw new Error('GOOGLE_AI_API_KEY não configurada');
    }

    // Implementação para Google Gemini Vision
    // Por enquanto, lança erro pois precisa de implementação específica
    throw new Error('Google Gemini não implementado ainda');
  }

  private buildPrompt(): string {
    return `
Analise esta nota fiscal brasileira e extraia as informações em formato JSON.

RESPONDA APENAS COM JSON VÁLIDO:

{
  "dataEmissao": "YYYY-MM-DD",
  "itens": ["produto1", "produto2"],
  "cep": "12345678",
  "confianca": 85
}

INSTRUÇÕES:

1. **dataEmissao**: Encontre a data de emissão da nota fiscal
   - Procure por: "Data de Emissão", "Emissão", "Data da NF" ou apenas uma data que indique ser a data da NF
   - Formato: YYYY-MM-DD ou null

2. **itens**: Liste os produtos/serviços comprados
   - Apenas descrições, sem valores
   - Máximo 10 itens principais

3. **cep**: Encontre o CEP no documento
   - Endereço do emitente ou destinatário
   - Apenas números (ex: "12345678")

4. **confianca**: Sua confiança na extração (0-100)
   - 90+: Muito confiante
   - 70-89: Confiante
   - 50-69: Moderado
   - <50: Pouco confiante

RESPONDA APENAS O JSON, SEM TEXTO ADICIONAL.
    `.trim();
  }

  private parseAIResponse(content: string, provider: string): AIAnalysisResult {
    try {
      // Remove possível texto antes/depois do JSON
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('JSON não encontrado na resposta');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      return {
        dataEmissao: parsed.dataEmissao ? new Date(parsed.dataEmissao) : null,
        itens: Array.isArray(parsed.itens) ? parsed.itens : [],
        cep: parsed.cep || null,
        confianca: typeof parsed.confianca === 'number' ? parsed.confianca : 50,
        provider,
        rawResponse: content
      };
    } catch (error) {
      this.logger.error(`Erro ao processar resposta do ${provider}: ${error.message}`);
      
      // Fallback: tenta extrair informações básicas
      return {
        dataEmissao: this.extractDateFallback(content),
        itens: this.extractItemsFallback(content),
        cep: this.extractCepFallback(content),
        confianca: 20,
        provider: `${provider}-fallback`,
        rawResponse: content
      };
    }
  }

  private extractDateFallback(text: string): Date | null {
    const datePattern = /(\d{4})-(\d{2})-(\d{2})|(\d{2})\/(\d{2})\/(\d{4})/g;
    const match = datePattern.exec(text);
    
    if (match) {
      try {
        if (match[1]) {
          // YYYY-MM-DD
          return new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
        } else {
          // DD/MM/YYYY
          return new Date(parseInt(match[6]), parseInt(match[5]) - 1, parseInt(match[4]));
        }
      } catch (error) {
        return null;
      }
    }
    
    return null;
  }

  private extractItemsFallback(text: string): string[] {
    // Extração básica baseada em linhas que parecem produtos
    const lines = text.split('\n');
    const items: string[] = [];
    
    for (const line of lines) {
      const cleanLine = line.trim();
      if (cleanLine.length > 10 && cleanLine.length < 100 && 
          !cleanLine.includes('{') && !cleanLine.includes('}')) {
        items.push(cleanLine);
      }
    }
    
    return items.slice(0, 10);
  }

  private extractCepFallback(text: string): string | null {
    const cepPattern = /(\d{5})-?(\d{3})/g;
    const match = cepPattern.exec(text);
    
    return match ? match[1] + match[2] : null;
  }
}
