import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional, Min } from 'class-validator';

export class PaginationQueryDto {
  @ApiProperty({ example: 1, description: '<PERSON><PERSON>mer<PERSON> da página', default: 1, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page: number = 1;

  @ApiProperty({ example: 10, description: 'Limite de itens por página', default: 10, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit: number = 10;
}

export class PaginatedResponseDto<T> {
  @ApiProperty({ description: 'Dados paginados' })
  data: T[];

  @ApiProperty({ description: 'Metadados da paginação' })
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}