import { ApiProperty } from '@nestjs/swagger';

export class PostoResponseDto {
  @ApiProperty({ example: 'PIXELS ELETRONICA LTDA', description: 'Nome do posto' })
  nome: string;

  @ApiProperty({ example: 'Avenida Vasco da Gama', description: 'Endereço do posto' })
  contato_endereco: string;

  @ApiProperty({ example: '241', description: 'Número do endereço' })
  contato_numero: string;

  @ApiProperty({ example: 'B', description: 'Complemento do endereço' })
  contato_complemento: string;

  @ApiProperty({ example: 'Federação', description: 'Bairro' })
  contato_bairro: string;

  @ApiProperty({ example: 'SALVADOR', description: 'Cidade' })
  contato_cidade: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email do posto' })
  contato_email: string;

  @ApiProperty({ example: '(71)3245-0226', description: 'Telefone do posto' })
  fone: string;

  @ApiProperty({ example: '28739322000176', description: 'Código do posto' })
  codigo_posto: string;

  @ApiProperty({ example: '2.5714702971166683', description: 'Distância em km' })
  distance: string;
}

export class PostosFoundResponseDto {
  @ApiProperty({ example: 'POSTOS', description: 'Chave identificadora do tipo de resposta' })
  key: 'POSTOS';

  @ApiProperty({ type: [PostoResponseDto], description: 'Lista de postos encontrados' })
  data: PostoResponseDto[];
}

export class GarantiaNaoValidaResponseDto {
  @ApiProperty({ example: 'NAO_VALIDA', description: 'Chave identificadora do tipo de resposta' })
  key: 'NAO_VALIDA';

  @ApiProperty({ 
    description: 'Dados da garantia não válida',
    example: {
      message: 'Garantia não é válida',
      expired: '15-01-2024'
    }
  })
  data: {
    message: string;
    expired: string;
  };
}

export class ProdutoNaoEncontradoResponseDto {
  @ApiProperty({ example: 'NAO_ENCONTRADO', description: 'Chave identificadora do tipo de resposta' })
  key: 'NAO_ENCONTRADO';

  @ApiProperty({ 
    description: 'Dados do produto não encontrado',
    example: {
      message: 'Produto não encontrado na nota fiscal'
    }
  })
  data: {
    message: string;
  };
}

export class ProtocoloCriadoResponseDto {
  @ApiProperty({ example: 'PROTOCOLO', description: 'Chave identificadora do tipo de resposta' })
  key: 'PROTOCOLO';

  @ApiProperty({ 
    description: 'Dados do protocolo criado',
    example: {
      message: 'Protocolo criado com sucesso',
      protocolo: '123456'
    }
  })
  data: {
    message: string;
    protocolo: string;
  };
}

export type ProcessNotaFiscalResponseDto = 
  | PostosFoundResponseDto 
  | GarantiaNaoValidaResponseDto 
  | ProdutoNaoEncontradoResponseDto 
  | ProtocoloCriadoResponseDto;
