import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { IsValidUF } from '../../common/validators/uf.validator';

export class UpdateClienteDto {
  @ApiProperty({ example: '<PERSON>', description: 'Nome do cliente' })
  @IsNotEmpty()
  @IsString()
  nome: string;

  @ApiProperty({ example: '123.456.789-00', description: 'CPF do cliente' })
  @IsNotEmpty()
  @IsString()
  cpf: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email do cliente' })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ example: '(11) 98765-4321', description: 'Telefone do cliente' })
  @IsNotEmpty()
  @IsString()
  telefone: string;

  @ApiProperty({ example: '01234-567', description: 'CEP' })
  @IsNotEmpty()
  @IsString()
  cep: string;

  @ApiProperty({ example: '123', description: 'Número do endereço' })
  @IsNotEmpty()
  @IsString()
  numero: string;

  @ApiProperty({ example: 'Apto 101', description: 'Complemento do endereço', required: false })
  @IsOptional()
  @IsString()
  complemento?: string;

  @ApiProperty({ example: 'Centro', description: 'Endereco' })
  @IsNotEmpty()
  @IsString()
  endereco: string;

  @ApiProperty({ example: 'São Paulo', description: 'Cidade' })
  @IsNotEmpty()
  @IsString()
  cidade: string;

  @ApiProperty({
    example: 'SP',
    description: 'Estado (UF) - deve ser uma UF válida do Brasil',
    enum: ['AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO']
  })
  @IsNotEmpty()
  @IsString()
  @IsValidUF()
  uf: string;

  @ApiProperty({ example: '133131', description: 'Código do cliente' })
  @IsNotEmpty()
  @IsString()
  codigoCliente: string;
}
