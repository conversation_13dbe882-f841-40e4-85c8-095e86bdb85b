import { Modu<PERSON> } from '@nestjs/common';
import { ProdutoController } from './produto.controller';
import { ProdutoService } from './produto.service';
import { ProdutoIntegrationService } from './services/produto-integration.service';
import { IntegrationConfigService } from '../common/services/integration-config.service';

@Module({
  controllers: [ProdutoController],
  providers: [
    ProdutoService,
    ProdutoIntegrationService,
    IntegrationConfigService,
  ],
  exports: [ProdutoService],
})
export class ProdutoModule {}

