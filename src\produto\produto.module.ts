import { Modu<PERSON> } from '@nestjs/common';
import { ProdutoController } from './produto.controller';
import { ProdutoService } from './produto.service';
import { ProdutoIntegrationService } from './services/produto-integration.service';
import { IntegrationConfigService } from '../common/services/integration-config.service';
import { OcrService } from './services/ocr.service';
import { GarantiaValidationService } from './services/garantia-validation.service';
import { ProdutoComparisonService } from './services/produto-comparison.service';
import { NotaFiscalProcessorService } from './services/nota-fiscal-processor.service';
import { AIDocumentAnalyzerService } from './services/ai-document-analyzer.service';
import { PostoModule } from '../posto/posto.module';

@Module({
  imports: [PostoModule],
  controllers: [ProdutoController],
  providers: [
    ProdutoService,
    ProdutoIntegrationService,
    IntegrationConfigService,
    OcrService,
    GarantiaValidationService,
    ProdutoComparisonService,
    NotaFiscalProcessorService,
    AIDocumentAnalyzerService,
  ],
  exports: [ProdutoService],
})
export class ProdutoModule {}

