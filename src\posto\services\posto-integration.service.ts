import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { IntegrationConfigService } from '../../common/services/integration-config.service';
import { PostoDto } from '../dto/posto.dto';

@Injectable()
export class PostoIntegrationService {
  private readonly logger = new Logger(PostoIntegrationService.name);

  constructor(private readonly configService: IntegrationConfigService) {}

  async findPostoMaisProximo(linha: string, cep: string): Promise<PostoDto[]> {
    try {
      const headers = this.configService.getIntegrationHeaders();

      this.logger.log(`Buscando posto mais próximo na API externa: linha ${linha}, CEP ${cep}`);

      const response: AxiosResponse<PostoDto[]> = await axios.get(
        `http://api2.telecontrol.com.br/institucional/postoMaisProximo/linha/${linha}/cep/${cep}`,
        { headers }
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Erro ao buscar posto mais próximo: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar posto mais próximo');
    }
  }

  async findPostoMaisProximoByCep(cep: string): Promise<PostoDto[]> {
    try {
      const headers = this.configService.getIntegrationHeaders();

      this.logger.log(`Buscando posto mais próximo na API externa: CEP ${cep}`);

      const response: AxiosResponse<PostoDto[]> = await axios.get(
        `http://api2.telecontrol.com.br/institucional/postoMaisProximo/cep/${cep}`,
        { headers }
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Erro ao buscar posto mais próximo por CEP: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar posto mais próximo por CEP');
    }
  }

  private handleApiError(error: any, message: string): never {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      if (status === 404) {
        throw new HttpException('Posto não encontrado', HttpStatus.NOT_FOUND);
      }
      
      throw new HttpException(
        `${message}: ${data?.message || error.message}`,
        status >= 500 ? HttpStatus.INTERNAL_SERVER_ERROR : HttpStatus.BAD_REQUEST
      );
    }
    
    throw new HttpException(
      `${message}: ${error.message}`,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}
