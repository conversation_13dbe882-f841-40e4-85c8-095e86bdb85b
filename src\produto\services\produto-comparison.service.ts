import { Injectable, Logger } from '@nestjs/common';
import { compareTwoStrings, findBestMatch } from 'string-similarity';
import { ProdutoDto } from '../dto/produto.dto';

export interface ProdutoMatch {
  produto: ProdutoDto;
  similarity: number;
  itemNotaFiscal: string;
}

export interface ComparisonResult {
  hasMatch: boolean;
  matches: ProdutoMatch[];
  unmatchedItems: string[];
  bestMatch?: ProdutoMatch;
}

@Injectable()
export class ProdutoComparisonService {
  private readonly logger = new Logger(ProdutoComparisonService.name);
  private readonly SIMILARITY_THRESHOLD = 0.6; // 60% de similaridade mínima

  compareProducts(
    itensNotaFiscal: string[], 
    produtosCatalogo: ProdutoDto[], 
    descricaoOpcional?: string
  ): ComparisonResult {
    try {
      this.logger.log(`Comparando ${itensNotaFiscal.length} itens da nota fiscal com ${produtosCatalogo.length} produtos do catálogo`);

      const matches: ProdutoMatch[] = [];
      const unmatchedItems: string[] = [];

      // Se há descrição opcional, adiciona aos itens para comparação
      const itensParaComparar = descricaoOpcional 
        ? [...itensNotaFiscal, descricaoOpcional]
        : itensNotaFiscal;

      for (const item of itensParaComparar) {
        const bestMatch = this.findBestMatchForItem(item, produtosCatalogo);
        
        if (bestMatch && bestMatch.similarity >= this.SIMILARITY_THRESHOLD) {
          matches.push(bestMatch);
          this.logger.log(`Match encontrado: "${item}" -> "${bestMatch.produto.descricao}" (${(bestMatch.similarity * 100).toFixed(1)}%)`);
        } else {
          unmatchedItems.push(item);
          this.logger.log(`Nenhum match encontrado para: "${item}"`);
        }
      }

      // Encontra o melhor match geral
      const bestMatch = matches.length > 0 
        ? matches.reduce((prev, current) => 
            prev.similarity > current.similarity ? prev : current
          )
        : undefined;

      const result: ComparisonResult = {
        hasMatch: matches.length > 0,
        matches,
        unmatchedItems,
        bestMatch
      };

      this.logger.log(`Resultado da comparação: ${matches.length} matches encontrados`);
      return result;

    } catch (error) {
      this.logger.error(`Erro ao comparar produtos: ${error.message}`, error.stack);
      return {
        hasMatch: false,
        matches: [],
        unmatchedItems: itensNotaFiscal
      };
    }
  }

  private findBestMatchForItem(item: string, produtos: ProdutoDto[]): ProdutoMatch | null {
    if (!item || !produtos.length) return null;

    const itemNormalizado = this.normalizeText(item);
    let bestSimilarity = 0;
    let bestProduto: ProdutoDto | null = null;

    for (const produto of produtos) {
      // Compara com a descrição do produto
      const descricaoNormalizada = this.normalizeText(produto.descricao);
      const similarityDescricao = compareTwoStrings(itemNormalizado, descricaoNormalizada);

      // Compara com a referência do produto
      const referenciaNormalizada = this.normalizeText(produto.referencia);
      const similarityReferencia = compareTwoStrings(itemNormalizado, referenciaNormalizada);

      // Compara com a família do produto
      const familiaNormalizada = this.normalizeText(produto.familia);
      const similarityFamilia = compareTwoStrings(itemNormalizado, familiaNormalizada);

      // Usa a maior similaridade entre descrição, referência e família
      const maxSimilarity = Math.max(similarityDescricao, similarityReferencia, similarityFamilia);

      if (maxSimilarity > bestSimilarity) {
        bestSimilarity = maxSimilarity;
        bestProduto = produto;
      }
    }

    if (bestProduto && bestSimilarity > 0) {
      return {
        produto: bestProduto,
        similarity: bestSimilarity,
        itemNotaFiscal: item
      };
    }

    return null;
  }

  private normalizeText(text: string): string {
    if (!text) return '';
    
    return text
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove acentos
      .replace(/[^a-z0-9\s]/g, '') // Remove caracteres especiais
      .replace(/\s+/g, ' ') // Normaliza espaços
      .trim();
  }

  // Método auxiliar para buscar produtos por palavras-chave
  findProductsByKeywords(keywords: string[], produtos: ProdutoDto[]): ProdutoDto[] {
    const foundProducts: ProdutoDto[] = [];
    
    for (const keyword of keywords) {
      const keywordNormalizado = this.normalizeText(keyword);
      
      for (const produto of produtos) {
        const descricaoNormalizada = this.normalizeText(produto.descricao);
        const familianormalizada = this.normalizeText(produto.familia);
        
        if (descricaoNormalizada.includes(keywordNormalizado) || 
            familianormalizada.includes(keywordNormalizado)) {
          if (!foundProducts.find(p => p.id === produto.id)) {
            foundProducts.push(produto);
          }
        }
      }
    }
    
    return foundProducts;
  }
}
