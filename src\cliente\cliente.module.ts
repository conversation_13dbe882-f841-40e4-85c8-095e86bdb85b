import { <PERSON>du<PERSON> } from '@nestjs/common';
import { <PERSON>lienteController } from './cliente.controller';
import { ClienteService } from './cliente.service';
import { ClienteIntegrationService } from './services/cliente-integration.service';
import { IntegrationConfigService } from '../common/services/integration-config.service';

@Module({
  controllers: [ClienteController],
  providers: [
    ClienteService,
    ClienteIntegrationService,
    IntegrationConfigService,
  ],
  exports: [ClienteService],
})
export class ClienteModule {}
