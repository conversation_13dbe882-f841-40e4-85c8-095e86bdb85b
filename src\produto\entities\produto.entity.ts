import { <PERSON>tity, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';
import { Protocolo } from '../../protocolo/entities/protocolo.entity';

@Entity()
export class Produto {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  nome: string;

  @Column({ nullable: true, unique: true })
  codigo: string;

  @Column()
  descricao: string;

  @OneToMany(() => Protocolo, protocolo => protocolo.produto)
  protocolos: Protocolo[];

}


