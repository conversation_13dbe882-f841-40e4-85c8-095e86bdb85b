import { Injectable, Logger } from '@nestjs/common';
import { OcrService } from './ocr.service';
import { GarantiaValidationService } from './garantia-validation.service';
import { ProdutoComparisonService } from './produto-comparison.service';
import { ProdutoIntegrationService } from './produto-integration.service';
import { PostoService } from '../../posto/posto.service';
import { ProcessNotaFiscalDto } from '../dto/process-nota-fiscal.dto';
import { 
  ProcessNotaFiscalResponseDto,
  PostosFoundResponseDto,
  GarantiaNaoValidaResponseDto,
  ProdutoNaoEncontradoResponseDto,
  ProtocoloCriadoResponseDto,
  PostoResponseDto
} from '../dto/process-response.dto';
import { PostoDto } from '../../posto/dto/posto.dto';

@Injectable()
export class NotaFiscalProcessorService {
  private readonly logger = new Logger(NotaFiscalProcessorService.name);

  constructor(
    private readonly ocrService: OcrService,
    private readonly garantiaValidationService: GarantiaValidationService,
    private readonly produtoComparisonService: ProdutoComparisonService,
    private readonly produtoIntegrationService: ProdutoIntegrationService,
    private readonly postoService: PostoService,
  ) {}

  async processNotaFiscal(processDto: ProcessNotaFiscalDto): Promise<ProcessNotaFiscalResponseDto> {
    try {
      this.logger.log('Iniciando processamento da nota fiscal');

      // 1. Processar OCR da nota fiscal
      const notaFiscalData = await this.ocrService.processNotaFiscal(processDto.notaFiscalBase64);
      
      if (!notaFiscalData.dataEmissao) {
        this.logger.warn('Data de emissão não encontrada na nota fiscal');
        return this.createProdutoNaoEncontradoResponse('Data de emissão não encontrada na nota fiscal');
      }

      // 2. Validar garantia (6 meses)
      const garantiaResult = this.garantiaValidationService.validateGarantia(notaFiscalData.dataEmissao);
      
      if (!garantiaResult.isValid) {
        this.logger.log('Garantia não é válida');
        return this.createGarantiaNaoValidaResponse(garantiaResult.dataExpiracao!);
      }

      // 3. Buscar produtos do catálogo
      const produtosCatalogo = await this.produtoIntegrationService.findAllProdutos();
      
      if (!produtosCatalogo || produtosCatalogo.length === 0) {
        this.logger.warn('Nenhum produto encontrado no catálogo');
        return this.createProdutoNaoEncontradoResponse('Catálogo de produtos não disponível');
      }

      // 4. Comparar produtos da nota fiscal com o catálogo
      const comparisonResult = this.produtoComparisonService.compareProducts(
        notaFiscalData.itens,
        produtosCatalogo,
        processDto.descricao
      );

      if (!comparisonResult.hasMatch) {
        this.logger.log('Nenhum produto da nota fiscal encontrado no catálogo');
        return this.createProdutoNaoEncontradoResponse('Produto não encontrado na nota fiscal');
      }

      // 5. Determinar CEP para busca de postos
      const cepParaBusca = notaFiscalData.cep || processDto.cep;
      
      if (!cepParaBusca) {
        this.logger.warn('CEP não encontrado na nota fiscal nem fornecido');
        return this.createProdutoNaoEncontradoResponse('CEP não encontrado para busca de postos');
      }

      // 6. Buscar postos próximos
      const postos = await this.postoService.findPostoMaisProximoByCep(cepParaBusca);
      
      if (postos && postos.length > 0) {
        this.logger.log(`${postos.length} postos encontrados`);
        return this.createPostosFoundResponse(postos);
      }

      // 7. Se não encontrou postos, preparar para criação de protocolo
      this.logger.log('Nenhum posto encontrado, preparando para criação de protocolo');
      return this.createProtocoloPreparadoResponse();

    } catch (error) {
      this.logger.error(`Erro ao processar nota fiscal: ${error.message}`, error.stack);
      return this.createProdutoNaoEncontradoResponse('Erro interno ao processar nota fiscal');
    }
  }

  private createPostosFoundResponse(postos: PostoDto[]): PostosFoundResponseDto {
    const postosResponse: PostoResponseDto[] = postos.map(posto => ({
      nome: posto.nome,
      contato_endereco: posto.contato_endereco,
      contato_numero: posto.contato_numero,
      contato_complemento: posto.contato_complemento,
      contato_bairro: posto.contato_bairro,
      contato_cidade: posto.contato_cidade,
      contato_email: posto.contato_email,
      fone: posto.fone,
      codigo_posto: posto.codigo_posto,
      distance: posto.distance
    }));

    return {
      key: 'POSTOS',
      data: postosResponse
    };
  }

  private createGarantiaNaoValidaResponse(dataExpiracao: string): GarantiaNaoValidaResponseDto {
    return {
      key: 'NAO_VALIDA',
      data: {
        message: 'Garantia não é válida',
        expired: dataExpiracao
      }
    };
  }

  private createProdutoNaoEncontradoResponse(message: string): ProdutoNaoEncontradoResponseDto {
    return {
      key: 'NAO_ENCONTRADO',
      data: {
        message
      }
    };
  }

  private createProtocoloPreparadoResponse(): ProtocoloCriadoResponseDto {
    // Por enquanto, apenas retorna uma mensagem indicando que o protocolo seria criado
    // A implementação real da criação do protocolo pode ser adicionada posteriormente
    return {
      key: 'PROTOCOLO',
      data: {
        message: 'Protocolo preparado para criação',
        protocolo: 'PENDING'
      }
    };
  }
}
